import apiClient from "./axiosInstance";

export const activityService = {
    createActivity: async ({ type, duration }) => {
        try {
            await apiClient.post("/activities", {
                type,
                duration,
            });

            return {
                success: true,
            }
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error creating activity"
            }
        }
    },

    updateActivity: async ({ id, type, duration }) => {
        try {
            await apiClient.put(`/activities/${id}`, {
                type,
                duration,
            });

            return {
                success: true,
            }
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error updating activity"
            }
        }
    },

    deleteActivity: async (id) => {
        try {
            await apiClient.delete(`/activities/${id}`);

            return {
                success: true,
            }
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error deleting activity"
            }
        }
    },
};