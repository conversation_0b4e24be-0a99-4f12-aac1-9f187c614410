import apiClient from "./axiosInstance";

export const activityService = {
    // Create a new manual activity entry
    createActivity: async ({ activityType, durationInMinutes }) => {
        try {
            const res = await apiClient.post("/user-health/activity-manual", {
                activityType,
                durationInMinutes,
            });

            return {
                success: true,
                message: res?.msg || "Activity created successfully",
                data: res?.data || null,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error creating activity"
            };
        }
    },

    // Get all activities
    getAllActivities: async () => {
        try {
            const res = await apiClient.get("/user-health/activity-manual/");

            return {
                success: true,
                data: res?.data || null,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activities"
            };
        }
    },

    // Get activity history for a specific date
    getActivityHistory: async (date) => {
        try {
            const res = await apiClient.get(`/user-health/activity-manual?date=${date}`);

            return {
                success: true,
                data: res?.data || [],
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activity history"
            };
        }
    },

    // Get activity highlights/summary
    getActivitySummary: async () => {
        try {
            const res = await apiClient.get("/user-health/activity-manual/summary");
            console.log("getActivitySummary ===", res);
            return {
                success: true,
                data: res?.data || null,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activity summary"
            };
        }
    },

    updateActivity: async ({ id, type, duration }) => {
        try {
            await apiClient.put(`/activities/${id}`, {
                type,
                duration,
            });

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error updating activity"
            };
        }
    },

    deleteActivity: async (id) => {
        try {
            await apiClient.delete(`/activities/${id}`);

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error deleting activity"
            };
        }
    },
};