import apiClient from "./axiosInstance";

export const highlightsService = {
  getHighlights: async (filter = "mood") => {
    try {
      const response = await apiClient.get(`/user_highlights/${filter}`);

      return {
        success: true,
        data: response?.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || `Error fetching ${filter} highlights`,
      };
    }
  },
};
