import apiClient from './axiosInstance';
import * as SecureStore from 'expo-secure-store';

const apiUrl = process.env.EXPO_PUBLIC_API_URL;

const chatbotService = {
    // Send a message to the chatbot
    sendMessage: async (message) => {
        try {
            const token = await SecureStore.getItemAsync('accessToken');

            const response = await apiClient.post(`${apiUrl}/chatbot/message`, {
                message: message
            }, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            return {
                success: true,
                data: response.data,
            };
        } catch (error) {
            console.error('sendMessage error:', error);
            return {
                success: false,
                message: error.message || 'Error sending message to chatbot',
                error: error.response?.data,
            };
        }
    },

    // Send a message with attachments to the chatbot
    sendMessageWithAttachments: async (message, attachments) => {
        try {
            const token = await SecureStore.getItemAsync('accessToken');

            const formData = new FormData();
            formData.append('message', message);

            // Add attachments to form data
            attachments.forEach((file, index) => {
                formData.append('attachments', {
                    uri: file.uri,
                    name: file.name || `file_${index}.${file.uri.split('.').pop()}`,
                    type: file.type || file.mimeType || 'application/octet-stream',
                });
            });

            const response = await apiClient.post(`${apiUrl}/chatbot/message/attachment`, formData, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'multipart/form-data',
                },
            });

            return {
                success: true,
                data: response.data,
            };
        } catch (error) {
            console.error('sendMessageWithAttachments error:', error);
            return {
                success: false,
                message: error.message || 'Error sending message with attachments to chatbot',
                error: error.response?.data,
            };
        }
    },

    // Get predefined options for the chatbot
    getPredefinedOptions: async () => {
        try {
            const token = await SecureStore.getItemAsync('accessToken');

            const response = await apiClient.get(`${apiUrl}/chatbot/options`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
            });

            return {
                success: true,
                data: response.data,
            };
        } catch (error) {
            console.error('getPredefinedOptions error:', error);

            // For development, return mock data if API is not available
            return {
                success: true,
                data: {
                    options: [
                        { id: '1', text: 'How to track my nutrition?' },
                        { id: '2', text: 'Help with device pairing' },
                        { id: '3', text: 'How to set up reminders?' },
                        { id: '4', text: 'Track my mood and sleep' },
                        { id: '5', text: 'Contact support team' }
                    ]
                },
            };
        }
    },

    // Get chat history
    getChatHistory: async () => {
        try {
            const token = await SecureStore.getItemAsync('accessToken');

            const response = await apiClient.get(`${apiUrl}/chatbot/history`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
            });

            return {
                success: true,
                data: response.data,
            };
        } catch (error) {
            console.error('getChatHistory error:', error);
            return {
                success: false,
                message: error.message || 'Error fetching chat history',
                error: error.response?.data,
            };
        }
    }
};

export default chatbotService;
