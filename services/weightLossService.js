import getDayName from "utils/dateandtimeformatters/getDayName";
import apiClient from "./axiosInstance";
import { getTimePeriod } from "utils/userrecords/getTimePeriod";

export const weightLossService = {
    logWeightData: async ({ date, weight }) => {
        try {
            const res = await apiClient.post("/weight_records", {
                "weight": weight,
                "date": date
            });

            return {
                success: true,
                message: res?.msg || "Weight data added successfully!!",
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error recording weight."
            };
        }
    },
    getLastLoggedData: async () => {
        try {
            const res = await apiClient.get("/weight_records");
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recent weight logged data."
            };
        }
    },
    getWeightRecordForDate: async (date) => {
        try {
            const res = await apiClient.get(`/weight_records?date=${date}`);

            return {
                success: true,
                exists: false,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching weight record."
            };
        }
    },
    checkWeightRecordExists: async (date) => {
        try {
            const res = await apiClient.get("/weight_records");

            if (res && res.data && Array.isArray(res.data)) {
                const recordExists = res.data.some(record => {
                    if (record.date) {
                        const recordDate = new Date(record.date).toISOString().split('T')[0];
                        return recordDate === date;
                    }
                    return false;
                });

                return {
                    success: true,
                    exists: recordExists,
                    message: recordExists ? "Weight already logged for this date" : ""
                };
            }

            return {
                success: true,
                exists: false
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error checking weight record."
            };
        }
    },
    updateWeightData: async ({ id, weight, date }) => {
        try {
            const res = await apiClient.put(`/weight_records/${id}`, {
                "weight": weight,
            });

            return {
                success: true,
                message: res?.msg || "Weight data updated successfully!!"
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error updating weight."
            };
        }
    },
    getWeightGraphData: async ({ filter }) => {
        try {
            const res = await apiClient.get(`/weight_analytics?filter=${filter}`);

            let responseData = [];
            let timeRange = null;

            if (filter === "weekly") {
                responseData = res.data.map((item) => {
                    return {
                        value: item?.averageWeight || 0,
                        label: getDayName(item.period).substring(0, 1)
                    };
                });

                timeRange = getTimePeriod(
                    res.data[0].period,
                    res.data[res.data.length - 1].period
                );
            } else if (filter === "monthly") {
                responseData = res.data.map((item) => {
                    const weekNumber = item.period.match(/week_(\d+)/);
                    return {
                        value: item?.averageWeight || 0,
                        label: `W${weekNumber[1]}`
                    };
                });

                timeRange = getTimePeriod(
                    res.data[0].startDate,
                    res.data[res.data.length - 1].endDate
                );
            } else if (filter === "half_yearly") {
                responseData = res.data.map((item) => {
                    return {
                        value: item?.averageWeight || 0,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange =
                    res.data[0].year !== res.data[res.data.length - 1].year
                        ? `${res.data[0].period} ${res.data[0].year} - ${res.data[res.data.length - 1].period} ${res.data[res.data.length - 1].year}`
                        : `${res.data[0].period} - ${res.data[res.data.length - 1].period} ${res.data[0].year}`;
            } else if (filter === "yearly") {
                responseData = res.data.map((item) => {
                    return {
                        value: item?.averageWeight || 0,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange = res.data[0].year !== res.data[res.data.length - 1].year
                    ? `${res.data[0].period} ${res.data[0].year} - ${res.data[res.data.length - 1].period} ${res.data[res.data.length - 1].year}`
                    : `${res.data[0].period} - ${res.data[res.data.length - 1].period} ${res.data[0].year}`;
            }

            return {
                success: true,
                data: {
                    weightData: responseData,
                    timeRange: timeRange
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching weight graph data."
            };
        }
    }
};
