const { default: apiClient } = require("./axiosInstance")

export const recipesService = {
    getAllRecipes: async ({ query, page = 1, mealType }) => {
        if (mealType === "My recipes") {
            return [];
        }
        if (mealType === "All") {
            mealType = "";
        }
        try {
            const res = await apiClient.get(
                `/recipes?page=${page}${query ? `&title=${query}` : ""
                }${mealType ? `&mealType=${mealType}` : ""
                }`
            );

            return {
                success: true,
                data: res?.data
            }
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recipes"
            }
        }
    },
    getSingleRecipe: async ({ id }) => {
        try {
            const res = await apiClient.get(`/recipes/${id}`);

            return {
                success: true,
                data: res?.data
            }
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recipe"
            }
        }
    },
    createUserRecipe: async ({ data }) => {
        try {
            const res = await apiClient.post(`/recipes`, data, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            console.log("Recipe created", res);

            return {
                success: true,
                data: res?.data
            }
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error creating recipe"
            }
        }
    },
}