import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
// import CryptoJS from 'crypto-js';
import * as CryptoJS from 'expo-crypto';
import apiClient from './axiosInstance';
import DeviceInfo, { getDeviceType } from 'react-native-device-info';
import { Platform } from 'react-native';
import * as Device from "expo-device";

const apiUrl = process.env.EXPO_PUBLIC_API_URL;
const encryptionKey = 'your-secure-key'; // Replace with a strong key

// Encrypt Data
const encryptData = (data) => {
    return CryptoJS.AES.encrypt(JSON.stringify(data), encryptionKey).toString();
};

// Decrypt Data
const decryptData = (encryptedData) => {
    try {
        const bytes = CryptoJS.AES.decrypt(encryptedData, encryptionKey);
        return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    } catch (error) {
        return null;
    }
};

const authService = {
    isAuthenticated: async () => {
        try {
            const token = await SecureStore.getItemAsync('userToken');
            console.log('isAuthenticated Service : ', token);
            return token !== null;
        } catch (error) {
            return false;
        }
    },

    isProfileCompleted: async () => {
        try {
            const userData = await SecureStore.getItemAsync('userData');
            // console.log('isProfileCompleted Service : ', userData);
            if (!userData) return false;

            // const user = decryptData(userData);
            return JSON.parse(userData); // Check if user profile is complete
        } catch (error) {
            return false;
        }
    },

    login: async ({ email, password, pushTokenString }) => {
        try {
            const response = await apiClient.post(`${apiUrl}/login`, { email, password });

            const { error, msg, accessToken, refreshToken, user } = response.data;

            if (error || !accessToken || !user) {
                throw new Error(msg || 'Invalid response from server');
            }

            // Securely store tokens & user data
            await SecureStore.setItemAsync('accessToken', accessToken);

            // Store refresh token if provided
            if (refreshToken) {
                await SecureStore.setItemAsync('refreshToken', refreshToken);
            }

            // Store user data
            await SecureStore.setItemAsync('userData', JSON.stringify(user));

            let notificationResult = {
                isNotificationActive: false
            };

            if (pushTokenString) {
                const deviceId = await DeviceInfo.getUniqueId();
                const deviceData = {
                    notificationToken: pushTokenString,
                    deviceId: deviceId,
                    deviceType: getDeviceType(Device.deviceType),
                    osType: Platform.OS,
                };

                const notificationResponse = await apiClient.post(`/notification_token`, deviceData);
                notificationResult = notificationResponse.data || notificationResult;
            }

            return {
                success: true,
                msg,
                user,
                isNotificationActive: notificationResult.isNotificationActive
            };

        } catch (error) {
            console.error('Login failed:', error.message || 'Unknown error');
            throw new Error('Login failed. Please try again.');
        }
    },

    refreshAccessToken: async () => {
        try {
            const refreshToken = await SecureStore.getItemAsync('refreshToken');
            if (!refreshToken) throw new Error('No refresh token available.');
            console.log('refreshToken ===', refreshToken);
            const response = await axios.post(`${apiUrl}/refresh-token`, { refreshToken });
            const { accessToken } = response.data;

            if (!accessToken) throw new Error('Failed to refresh token.');

            // Store the new token in both places for compatibility
            await SecureStore.setItemAsync('userToken', accessToken);
            await SecureStore.setItemAsync('accessToken', accessToken);

            console.log('Token refreshed successfully.');
            return accessToken;

        } catch (error) {
            console.error('Token refresh failed:', error.message);

            // Clear tokens on refresh failure
            await SecureStore.deleteItemAsync('userToken');
            await SecureStore.deleteItemAsync('accessToken');
            await SecureStore.deleteItemAsync('refreshToken');

            throw new Error('Session expired. Please log in again.');
        }
    },

    getUser: async (userId) => {
        try {
            const token = await SecureStore.getItemAsync('userToken') || await authService.refreshAccessToken();
            const response = await axios.get(`${apiUrl}/users/${userId}`, {
                headers: { Authorization: `Bearer ${token}` }
            });
            return response.data;
        } catch (error) {
            throw error.response?.data || error.message;
        }
    },

    checkUserExists: async (email) => {
        try {
            const response = await axios.get(`${apiUrl}/check-user?email=${email}`);
            return response.data.exists;
        } catch (error) {
            throw error.response?.data || error.message;
        }
    },

    createUser: async (userData) => {
        try {
            const response = await axios.post(`${apiUrl}/register`, userData);
            console.log('User registered successfully.');
            return response.data;
        } catch (error) {
            throw error.response?.data || error.message;
        }
    },

    updateUser: async (userId, updateData) => {
        try {
            const token = await SecureStore.getItemAsync('userToken') || await authService.refreshAccessToken();
            const response = await axios.put(`${apiUrl}/users/${userId}`, updateData, {
                headers: { Authorization: `Bearer ${token}` }
            });
            return response.data;
        } catch (error) {
            throw error.response?.data || error.message;
        }
    },
    forgotPassword: async (email) => {
        try {
            const response = await axios.put(`${apiUrl}/forget_password`, { email });
            return { success: true, message: response.data.message };
        } catch (error) {
            return { success: false, message: error?.response?.data?.message || 'Password reset failed' };
        }
    },
    logout: async () => {
        await SecureStore.deleteItemAsync('userToken');
        await SecureStore.deleteItemAsync('refreshToken');
        await SecureStore.deleteItemAsync('userData');
        console.log('User logged out successfully.');
    },
    resendVerificationLink: async (email) => {
        try {
            const response = await axios.put(`${apiUrl}/resend_verification_email`, { email });
            return { success: true, message: response.data.message };
        } catch (error) {
            console.log('error', error.response?.data);
            throw error.response?.data || { success: false, message: 'Password reset failed' };
        }
    },
    updateUserTimeZone: async () => {
        try {
            await apiClient.put(`${apiUrl}/timezone_update`, { timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone });
            return {
                success: true
            };
        }
        catch (error) {
            return {
                success: false,
                error: error?.message || 'Error updating timezone'
            };
        }
    }
};

export default authService;
