import apiClient from "./axiosInstance";
const apiUrl = process.env.EXPO_PUBLIC_API_URL;

const deviceService = {
  getDevices: async () => {
    try {
      const res = await apiClient.get(`${apiUrl}/devices`);
      return {
        success: true,
        data: res.devices,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || "Error fetching goals",
      };
    }
  },

  addDevice: async (goalsData) => {
    try {
      const res = await apiClient.post(`${apiUrl}/devices`, goalsData);
      return {
        success: true,
        data: res.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || "Error updating goals",
      };
    }
  },
  removeDevice: async () => {
    try {
      const res = await apiClient.put(`${apiUrl}/devices`);
      return {
        success: true,
        data: res.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || "Error updating goals",
      };
    }
  },
  getPermission: async () => {
    try {
      const res = await apiClient.get(`${apiUrl}/app_permissions`);
      return {
        success: true,
        data: res.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || "Error updating goals",
      };
    }
  },
  updatePermission: async (permission) => {
    try {
      const res = await apiClient.put(
        `${apiUrl}/app_permissions/update`,
        permission
      );
      return {
        success: true,
        data: res.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || "Error updating goals",
      };
    }
  },
};

export default deviceService;
