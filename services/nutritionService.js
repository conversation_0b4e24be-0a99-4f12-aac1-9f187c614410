import apiClient from './axiosInstance';
const apiUrl = process.env.EXPO_PUBLIC_API_URL;
console.log('apiUrl ===', apiUrl);

const deviceService = {
    getMeals: async () => {
        try {
            const res = await apiClient.get(`${apiUrl}/devices`);
            return {
                success: true,
                data: res.devices
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data || 'Error fetching goals'
            };
        }
    },
    addMeal: async (goalsData) => {
        try {
            const res = await apiClient.post(`${apiUrl}/devices`, goalsData);
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data || 'Error updating goals'
            };
        }
    },
    updateMeal: async (goalsData) => {
        try {
            const res = await apiClient.post(`${apiUrl}/devices`, goalsData);
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data || 'Error updating goals'
            };
        }
    },

    deleteMeal: async (goalsData) => {
        try {
            console.log('deleteMeal ===', goalsData);
            const res = await apiClient.post(`${apiUrl}/devices`, goalsData);
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data || 'Error updating goals'
            };
        }
    },
    getReminders: async (goalsData) => {
        try {
            console.log('getReminders ===', goalsData);
            const res = await apiClient.put(`${apiUrl}/devices`, goalsData);
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data || 'Error updating goals'
            };
        }
    },
    updateReminder: async (goalsData) => {
        try {
            console.log('updateReminder ===', goalsData);
            const res = await apiClient.put(`${apiUrl}/app_permission`, goalsData);
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data || 'Error updating goals'
            };
        }
    },
};

export default deviceService;