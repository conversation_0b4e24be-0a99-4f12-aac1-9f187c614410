const { default: apiClient } = require("./axiosInstance");

export const reminderService = {
    getReminders: async (category, page) => {
        try {
            const res = await apiClient.get(`/reminders?category=${category}&page=${page}`);

            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error fetching remainders"
            };
        }
    },
    getSingleReminder: async (id) => {
        try {
            const res = await apiClient.get(`/reminders/${id}`);
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error fetching remainder,"
            };
        }
    },
    createReminder: async ({ category, label, timeStamps, timeZone, sound, redirectURL }) => {
        try {
            const date = new Date(timeStamps);

            let hour = date.getHours();
            const minute = date.getMinutes();
            const period = hour >= 12 ? 'PM' : 'AM';

            if (hour > 12) {
                hour -= 12;
            } else if (hour === 0) {
                hour = 12;
            }

            console.log(sound.ios);


            await apiClient.post("/reminders", {
                category: category,
                label: label,
                frontend_screen_url: redirectURL,
                userTime: {
                    "hour": hour,
                    "minute": minute,
                    "period": period,
                },
                timeZone: timeZone,
                sound: sound,
            });

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error creating remainder"
            };
        }
    },
    updateReminder: async ({ id, label, timeStamps, sound }) => {
        try {
            const date = new Date(timeStamps);

            let hour = date.getHours();
            const minute = date.getMinutes();
            const period = hour >= 12 ? 'PM' : 'AM';

            if (hour > 12) {
                hour -= 12;
            } else if (hour === 0) {
                hour = 12;
            }

            await apiClient.put(`/reminders/${id}`, {
                label: label,
                userTime: {
                    hour: hour,
                    minute: minute,
                    period: period,
                },
                sound: sound,
            });

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error updating remainder"
            };
        }
    },
    deleteRemainder: async (id) => {
        try {
            await apiClient.put(`/reminders/delete/${id}`);

            return {
                success: true,
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || "Error deleting remainder"
            };
        }
    },
    getLastReminder: async () => {
        try {
            const res = await apiClient.get("/meal_records");
            console.log("getLastReminder ===", res);
            return {
                success: true,
                data: res.data && res.data.data ? res.data.data : []
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching last food logs."
            };
        }
    }
};