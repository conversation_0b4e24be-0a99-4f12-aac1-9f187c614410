import getDayName from 'utils/dateandtimeformatters/getDayName';
import apiClient from './axiosInstance';
import { getTimePeriod } from 'utils/userrecords/getTimePeriod';

export const moodService = {
    getMoodOptions: async () => {
        try {
            const MoodOptions = [
                {
                    label: "Happy",
                    value: "Happy",
                },
                {
                    label: "Moderately Happy",
                    value: "Moderately Happy",
                },
                {
                    label: "Irritated",
                    value: "Irritated",
                },
                {
                    label: "Anxious",
                    value: "Anxious",
                },
                {
                    label: "Sad",
                    value: "Sad",
                }
            ];
            return {
                success: true,
                data: MoodOptions
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching mood options"
            };
        }
    },
    getHungerOptions: async () => {
        try {
            const HungerOptions = [
                {
                    label: "High",
                    value: "High",
                },
                {
                    label: "Moderate",
                    value: "Moderate",
                },
                {
                    label: "Barely",
                    value: "Barely",
                },
                {
                    label: "Mild",
                    value: "Mild",
                }
            ];
            return {
                success: true,
                data: HungerOptions
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching hunger options"
            };
        }
    },
    getLastMood: async () => {
        try {
            const response = await apiClient.get("/mood_records");
            return {
                success: true,
                data: response?.data || null
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching last mood"
            };
        }
    },

    createMood: async ({ mood, hungerLevel }) => {
        try {
            const response = await apiClient.post("/mood_records", {
                moodType: mood,
                hungerLevel: hungerLevel
            });
            return {
                success: true,
                data: response
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error saving mood."
            };
        }
    },

    editMood: async ({ mood, hungerLevel, id }) => {
        try {
            const response = await apiClient.put(`/mood_records/${id}`, {
                moodType: mood,
                hungerLevel: hungerLevel
            });
            return {
                success: true,
                data: response
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error editing mood."
            };
        }
    },

    getAllVideos: async ({ page = 1 }) => {
        try {
            const response = await apiClient.get(`/video_library?tag=wellness&page=${page}`);
            return {
                success: true,
                data: response?.videos
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching videos"
            };
        }
    },

    getRecommendedVideo: async () => {
        try {
            const response = await apiClient.get('/mood_recommendation');

            return {
                success: true,
                data: response?.data
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recommended video"
            };
        }
    },

    getMoodGraphData: async ({ filter }) => {
        try {
            const response = await apiClient.get(`/mood_analytics?filter=${filter}`,);

            let responseData = [];
            let timeRange = null;

            if (filter === "weekly") {
                responseData = response.data.map(item => {
                    return {
                        value: item.averageMoodType,
                        label: getDayName(item.period).substring(0, 1)
                    };
                });

                timeRange = getTimePeriod(response.data[0].period, response.data[response.data.length - 1].period);
            }
            else if (filter === "monthly") {
                responseData = response.data.map(item => {
                    const weekNumber = item.period.match(/week_(\d+)/);
                    return {
                        value: item?.averageMoodType || 0,
                        label: `W${weekNumber[1]}`
                    };
                });
                timeRange = getTimePeriod(response.data[0].startDate, response.data[response.data.length - 1].endDate);
            }
            else if (filter == "half_yearly") {
                responseData = response.data.map(item => {
                    return {
                        value: item?.averageMoodType || 0,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange = response.data[0].year !== response.data[response.data.length - 1].year
                    ? `${response.data[0].period.substring(0, 3)} ${response.data[0].year} - ${response.data[response.data.length - 1].period.substring(0, 3)} ${response.data[response.data.length - 1].year}`
                    : `${response.data[0].period.substring(0, 3)} - ${response.data[response.data.length - 1].period.substring(0, 3)} ${response.data[0].year}`;
            }
            else if (filter === "yearly") {
                responseData = response.data.map(item => {
                    return {
                        value: item?.averageMoodType || 0,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange = response.data[0].year !== response.data[response.data.length - 1].year
                    ? `${response.data[0].period} ${response.data[0].year} - ${response.data[response.data.length - 1].period} ${response.data[response.data.length - 1].year}`
                    : `${response.data[0].period} - ${response.data[response.data.length - 1].period} ${response.data[0].year}`;
            }

            return {
                success: true,
                data: {
                    moodData: responseData,
                    timeRange: timeRange
                }
            };
        } catch (error) {
            console.log(error);
            return {
                success: false,
                error: error?.message || "Error fetching recommended video"
            };
        }
    }
};

export default moodService;
