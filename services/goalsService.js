import apiClient from './axiosInstance';
const apiUrl = process.env.EXPO_PUBLIC_API_URL;
console.log('apiUrl ===', apiUrl);

const goalsService = {
    getGoals: async () => {
        try {
            const res = await apiClient.get(`${apiUrl}/goals`);
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data || 'Error fetching goals'
            };
        }
    },

    updateGoal: async (goalsData) => {
        try {
            const res = await apiClient.put(`${apiUrl}/goals`, goalsData);

            return {
                success: true,
                data: res.user
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data || 'Error updating goals'
            };
        }
    },
};

export default goalsService;