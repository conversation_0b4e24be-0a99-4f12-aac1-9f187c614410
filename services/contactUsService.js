import axios from 'axios';
import * as SecureStore from 'expo-secure-store';

const apiUrl = process.env.EXPO_PUBLIC_API_URL;

const contactUsService = {
    sendQuery: async (queryData) => {
        try {
            for (let pair of queryData.entries()) {
                console.log(`${pair[0]}:`, pair[1]);
            }

            const token = await SecureStore.getItemAsync('accessToken');

            const res = await axios.post(`${apiUrl}/contact`, queryData, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'multipart/form-data',
                },
            });

            return {
                success: true,
                data: res.data,
            };
        } catch (error) {
            console.error('sendQuery error ===', error);
            return {
                success: false,
                message: error.message || 'Error while sending query',
                error: error.response?.data,
            };
        }
    },
};

export default contactUsService;
