import React, { useEffect } from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { View, TouchableOpacity, StyleSheet, Image, BackHandler, Alert } from 'react-native';
import { Colors } from 'constants/theme/colors';
import BackIcon from 'assets/icons/back.png';
import HomeIcon from 'assets/icons/home.png';
import MenuIcon from 'assets/icons/menu.png';
import DashStackNav from './AppStackNav';
import Dashboard from 'screens/Dashboard/Dashboard';
import useLayoutStore from 'store/layoutStore';

const BottomTabStack = createBottomTabNavigator();

const CustomTabButton = ({ children, onPress }) => (
    <TouchableOpacity style={styles.customTabButton} onPress={onPress}>
        {children}
    </TouchableOpacity>
);

const TabImage = ({ source, focused }) => (
    <Image source={source} style={[styles.icon, focused && styles.focusedIcon]} />
);

const BottomTabStackNav = () => {
    const navigation = useNavigation();
    const { isMenuOpen, toggleMenu, isSettingOpen, toggleSettings } = useLayoutStore(state => state);

    const handleBackPress = () => {
        if (isMenuOpen) {
            toggleMenu();
            return;
        }
        if (isSettingOpen) {
            toggleSettings();
            return;
        }
        const currentRoute = navigation.getCurrentRoute()?.name;

        if (navigation.canGoBack() && currentRoute !== 'home' && currentRoute !== 'dashboard') {
            navigation.goBack();
        } else {
            Alert.alert(
                "Exit App",
                "Are you sure you want to exit?",
                [
                    { text: "Cancel", style: "cancel" },
                    { text: "Exit", onPress: () => BackHandler.exitApp() }
                ]
            );
        }
    };

    // Listen to the hardware back button for Android
    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                handleBackPress();
                return true; // Prevent default back behavior
            };

            BackHandler.addEventListener("hardwareBackPress", onBackPress);
            return () => BackHandler.removeEventListener("hardwareBackPress", onBackPress);
        }, [])
    );

    return (
        <BottomTabStack.Navigator
            initialRouteName="home"
            screenOptions={{
                headerShown: false,
                tabBarShowLabel: false,
                tabBarHideOnKeyboard: true,
                tabBarStyle: styles.tabBar,
                tabBarButton: (props) => <CustomTabButton {...props} />,
            }}
        >
            {/* Back Button in Bottom Tab */}
            <BottomTabStack.Screen
                name="back"
                component={DashStackNav} // Keep it inside DashStackNav to avoid blank screens
                listeners={{
                    tabPress: (e) => {
                        e.preventDefault(); // Prevent default navigation
                        handleBackPress(); // Call back function manually
                    },
                }}
                options={{ tabBarIcon: ({ focused }) => <TabImage source={BackIcon} focused={focused} /> }}
            />

            {/* Home Screen */}
            <BottomTabStack.Screen
                name="home"
                component={DashStackNav}
                options={{ tabBarIcon: ({ focused }) => <TabImage source={HomeIcon} focused={focused} /> }}
                listeners={{
                    tabPress: (e) => {
                        // e.preventDefault();

                        if (isMenuOpen) toggleMenu();
                        if (isSettingOpen) toggleSettings();

                        // if (isDashboardScreen()) {
                        //     console.log('Already on dashboard, maybe refresh data');
                        //     return;
                        // }

                        // if(navigation)
                        // navigation.reset({
                        //     index: 0,
                        //     routes: [{ name: 'dashboard' }],
                        // });
                        // navigation.navigate('dashboard');
                    },
                }}
            />

            {/* Menu Button */}
            <BottomTabStack.Screen
                name="menu"
                component={DashStackNav}
                listeners={{
                    tabPress: (e) => {
                        e.preventDefault();
                        toggleMenu();
                        if (isSettingOpen) toggleSettings();
                    },
                }}
                options={{ tabBarIcon: ({ focused }) => <TabImage source={MenuIcon} focused={focused} /> }}
            />
        </BottomTabStack.Navigator>
    );
};

const styles = StyleSheet.create({
    tabBar: {
        backgroundColor: Colors.primaryGreen,
        elevation: 5,
        position: "absolute",
        bottom: 10,
        borderRadius: 22,
        marginHorizontal: '5%',
        height: 60,
        width: "90%",
        paddingBottom: 0,
        alignItems: "center",
        justifyContent: "center"
    },
    customTabButton: {
        flex: 1,
        // backgroundColor: "red",
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'transparent',
        marginHorizontal: 5,
        marginVertical: 5,
        borderRadius: 30,
        height: 50,
    },
    icon: {
        width: 26,
        height: 32,
        resizeMode: 'contain',
    },
    focusedIcon: {
        opacity: 1,
    },
});

export default BottomTabStackNav;