export const physicalOptions = [
    { label: "Lose 5 kg", value: "lose5" },
    { label: "Lose 10 kg", value: "lose10" },
    { label: "Lose 20 kg or more", value: "lose20" },
    { label: "Custom bodyweight goals", value: "custom" },
];

export const movementOptions = [
    { label: "Burn 100kcal", value: "burn100" },
    { label: "Burn 500kcal", value: "burn500" },
    { label: "Burn more than 500kcal", value: "burnMore" },
];

export const mindfulnessOptions = [
    { label: "Avoiding Distractions", value: "distractions" },
    { label: "Practice mindful eating", value: "mindfulEating" },
    { label: "Balancing food & activity", value: "balance" },
];

export const sleepOptions = [
    { label: "6 hours", value: "6" },
    { label: "7 hours", value: "7" },
    { label: "8 hours", value: "8" },
];

export const deviceUsageLimitOptions = [
    { label: "No device", value: "null" },
    { label: "2 hours", value: 2 },
    { label: "2.5 hours", value: 2.5 },
    { label: "3 hours", value: 3 },
];

export const dietOptions = [
    { label: "Vegan", value: "vegan" },
    { label: "Vegetarian", value: "veg" },
    { label: "Non-vegetarian", value: "non-veg" },
    { label: "Other", value: "others" },
];

export const devices = [
    { id: "000001", name: "Belt", version: "v01" },
    { id: "000002", name: "Belt", version: "v02" },
    { id: "000003", name: "Mark", version: "v01" },
];


export const Help = [
    {
        "id": "device",
        "title": "Device-Related Help",
        "description": "Connect, sync, or troubleshoot your devices",
        "topics": [
            {
                "id": "connect_device",
                "title": "Connecting Your Device",
                "description": "Step-by-step guide to pairing your device.Step-by-step guide to pairing your device."
            },
            {
                "id": "sync_issues",
                "title": "Sync Issues",
                "description": "Troubleshooting common sync problems.Troubleshooting common sync problems."
            },
            {
                "id": "battery_life",
                "title": "Battery Life Tips",
                "description": "How to extend battery life of your device.How to extend battery life of your device."
            },
            {
                "id": "device_life",
                "title": "Device Life Tips",
                "description": "How to extend battery life of your device.How to extend battery life of your device."
            },
            {
                "id": "ba_life",
                "title": "Battery Life Tips",
                "description": "How to extend battery life of your device.How to extend battery life of your device."
            },
            {
                "id": "dee_life",
                "title": "Device Life Tips",
                "description": "How to extend battery life of your device.How to extend battery life of your device."
            }


        ]
    },
    {
        "id": "integration",
        "title": "Integration Help",
        "description": "Setup Apple Health, Google Fit, and other integrations",
        "topics": [
            {
                "id": "apple_health",
                "title": "Apple Health Setup",
                "description": "Enable and sync data with Apple Health."
            },
            {
                "id": "google_fit",
                "title": "Google Fit Integration",
                "description": "How to connect with Google Fit."
            },
            {
                "id": "third_party_apps",
                "title": "Third-Party Apps",
                "description": "Connect with other fitness and health apps."
            }
        ]
    },
    {
        "id": "goals",
        "title": "Goals & Tracking",
        "description": "Set and manage your health goals",
        "topics": [
            {
                "id": "set_goals",
                "title": "Setting Goals",
                "description": "Define your fitness, sleep, and mindfulness goals."
            },
            {
                "id": "track_progress",
                "title": "Tracking Progress",
                "description": "Monitor your progress over time."
            },
            {
                "id": "adjust_goals",
                "title": "Adjusting Goals",
                "description": "Modify your goals based on your performance."
            }
        ]
    },
    {
        "id": "account",
        "title": "Account & Profile",
        "description": "Manage account settings and profile",
        "topics": [
            {
                "id": "update_profile",
                "title": "Updating Profile",
                "description": "Change your name, age, and other details."
            },
            {
                "id": "change_password",
                "title": "Change Password",
                "description": "Steps to update your password securely."
            },
            {
                "id": "delete_account",
                "title": "Delete Account",
                "description": "How to permanently remove your account."
            }
        ]
    }
];