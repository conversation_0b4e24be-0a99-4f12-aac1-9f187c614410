import { create } from 'zustand';
import chatbotService from 'services/chatbotService';

const useChatbotStore = create((set, get) => ({
    // Chat messages
    messages: [],
    isLoading: false,
    predefinedOptions: [],
    error: null,

    // Initialize chatbot
    initializeChatbot: async () => {
        set({ isLoading: true });
        try {
            // Get predefined options
            const optionsResponse = await chatbotService.getPredefinedOptions();
            if (optionsResponse.success) {
                set({ predefinedOptions: optionsResponse.data.options || [] });
            }

            // Get chat history
            const historyResponse = await chatbotService.getChatHistory();
            if (historyResponse.success) {
                set({ messages: historyResponse.data.messages || [] });
            } else {
                // If no history or error, add a welcome message
                set({
                    messages: [
                        {
                            id: 'welcome',
                            text: "Hi! This is 'Appebot', a chatbot from Appetec App. Really nice to meet you and thankyou for installing this app. I am here to provide you with any assistance in terms of help, questions, answers or facts just name it! Or choose from the following:",
                            sender: 'bot',
                            timestamp: new Date().toISOString(),
                        }
                    ]
                });
            }
        } catch (error) {
            console.error('Error initializing chatbot:', error);
            set({
                error: 'Failed to initialize chatbot',
                messages: [
                    {
                        id: 'welcome',
                        text: 'Hello! I\'m your health assistant. How can I help you today?',
                        sender: 'bot',
                        timestamp: new Date().toISOString(),
                    }
                ]
            });
        } finally {
            set({ isLoading: false });
        }
    },

    // Send a message
    sendMessage: async (message, attachments = []) => {
        if (!message && attachments.length === 0) return;

        // Add user message to the chat
        const userMessageId = Date.now().toString();
        set((state) => ({
            messages: [
                ...state.messages,
                {
                    id: userMessageId,
                    text: message,
                    sender: 'user',
                    attachments: attachments,
                    timestamp: new Date().toISOString(),
                }
            ],
            isLoading: true
        }));

        try {
            // Send message to API
            let response;
            if (attachments.length > 0) {
                response = await chatbotService.sendMessageWithAttachments(message, attachments);
            } else {
                response = await chatbotService.sendMessage(message);
            }

            // Add bot response to the chat
            if (response.success) {
                set((state) => ({
                    messages: [
                        ...state.messages,
                        {
                            id: Date.now().toString(),
                            text: response.data.message || 'I received your message.',
                            sender: 'bot',
                            timestamp: new Date().toISOString(),
                        }
                    ],
                    error: null
                }));
            } else {
                // If API call fails, add an error message
                set((state) => ({
                    messages: [
                        ...state.messages,
                        {
                            id: Date.now().toString(),
                            text: 'Sorry, I encountered an error processing your request. Please try again later.',
                            sender: 'bot',
                            timestamp: new Date().toISOString(),
                        }
                    ],
                    error: response.message || 'Failed to send message'
                }));
            }
        } catch (error) {
            console.error('Error sending message:', error);
            // Add error message to chat
            set((state) => ({
                messages: [
                    ...state.messages,
                    {
                        id: Date.now().toString(),
                        text: 'Sorry, I encountered an error processing your request. Please try again later.',
                        sender: 'bot',
                        timestamp: new Date().toISOString(),
                    }
                ],
                error: 'Failed to send message'
            }));
        } finally {
            set({ isLoading: false });
        }
    },

    // Send a predefined option
    sendPredefinedOption: async (option) => {
        await get().sendMessage(option.text);
    },

    // Clear chat history
    clearChat: () => {
        set({
            messages: [
                {
                    id: 'welcome',
                    text: 'Hello! I\'m your health assistant. How can I help you today?',
                    sender: 'bot',
                    timestamp: new Date().toISOString(),
                }
            ]
        });
    }
}));

export default useChatbotStore;
