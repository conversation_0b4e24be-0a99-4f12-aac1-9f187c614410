import { StyleSheet, Text, TouchableOpacity, View, Platform, Modal, Button } from 'react-native';
import React, { useState, useEffect } from 'react';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import DateTimePicker from '@react-native-community/datetimepicker';
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';

const CustomTimePickerWithLabel = ({
    label,
    selectedTime,
    onChangeTime,
    error,
    onPress,
    clearValidationError,
    disabled,
    maxTime,
    loading = false,
}) => {
    // State for tracking if picker is showing
    const [showTimePicker, setShowTimePicker] = useState(false);

    // Initialize with a default time or the selected time if available
    const defaultTime = new Date();
    defaultTime.setSeconds(0);
    defaultTime.setMilliseconds(0);

    // Use separate state for managing the time being picked (for iOS)
    const [pickerTime, setPickerTime] = useState(
        selectedTime ? new Date(selectedTime) : new Date(defaultTime)
    );

    // Use separate state for display time (what appears in the button)
    const [displayTime, setDisplayTime] = useState(
        selectedTime ? new Date(selectedTime) : null
    );

    // For iOS modal handling
    const [showIOSModal, setShowIOSModal] = useState(false);

    // Keep picker state in sync with props
    useEffect(() => {
        if (selectedTime) {
            const timeObj = new Date(selectedTime);
            setDisplayTime(timeObj);
            setPickerTime(timeObj);
        }
    }, [selectedTime]);

    // Function to format time for display
    const formatTimeForDisplay = (time) => {
        if (!time) return "Time";

        try {
            return time.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        } catch (error) {
            console.error("Error formatting time:", error);
            return "Time";
        }
    };

    // Function for Android time picker
    const handleAndroidTimeChange = (event, selectedTime) => {
        setShowTimePicker(false);

        if (event.type === 'dismissed' || !selectedTime) {
            return;
        }

        // Validate against maxTime if provided
        if (validateTimeAgainstMax(selectedTime)) {
            // Update display and notify parent
            setDisplayTime(selectedTime);
            onChangeTime(selectedTime);
        }
    };

    // Validate time against max time constraint
    const validateTimeAgainstMax = (timeToCheck) => {
        if (!maxTime || !timeToCheck) return true;

        const selectedDateTime = new Date(timeToCheck);
        const maxDateTime = new Date(maxTime);

        // Only compare hours and minutes
        const selectedTimeValue = selectedDateTime.getHours() * 60 + selectedDateTime.getMinutes();
        const maxTimeValue = maxDateTime.getHours() * 60 + maxDateTime.getMinutes();

        // If selected time is in the future, show error
        if (selectedTimeValue > maxTimeValue) {
            if (clearValidationError) {
                clearValidationError();
                setTimeout(() => {
                    onChangeTime(null); // Clear the invalid time
                    if (onPress) onPress(); // Trigger any additional logic in parent
                }, 0);
            }
            return false;
        }

        return true;
    };

    // iOS picker change handler - just update the picker state
    const handleIOSTimeChange = (event, selectedTime) => {
        if (selectedTime) {
            setPickerTime(selectedTime);
        }
    };

    // Handle confirming the iOS time selection
    const handleIOSConfirm = () => {
        // Validate the final time
        if (validateTimeAgainstMax(pickerTime)) {
            // Update display time and notify parent
            setDisplayTime(pickerTime);
            onChangeTime(pickerTime);
        }

        // Close the modal
        setShowIOSModal(false);
    };

    // Handle canceling the iOS time selection
    const handleIOSCancel = () => {
        // Reset picker time to display time
        if (displayTime) {
            setPickerTime(new Date(displayTime));
        } else {
            setPickerTime(new Date(defaultTime));
        }

        // Close the modal
        setShowIOSModal(false);
    };

    // Handle opening the time picker
    const handleOpenPicker = () => {
        if (clearValidationError) {
            clearValidationError();
        }

        if (onPress) {
            onPress();
        }

        if (Platform.OS === 'ios') {
            // For iOS, show modal with picker
            setShowIOSModal(true);
        } else {
            // For Android, show native picker
            setShowTimePicker(true);
        }
    };

    return (
        <View style={{ marginTop: 10 }}>
            <Text style={styles.inputLabel}>
                {label}
            </Text>
            <SkeletonItem isLoading={loading} height={44} borderRadius={25} style={{ marginVertical: 10 }}>
                <>
                    <TouchableOpacity
                        activeOpacity={.95}
                        onPress={handleOpenPicker}
                        disabled={disabled}
                    >
                        <Text style={[styles.inputBtn, {
                            fontFamily: displayTime ? ThemeFonts.Exo_400 : ThemeFonts.Exo_400_Italic
                        }]}>
                            {formatTimeForDisplay(displayTime)}
                        </Text>
                    </TouchableOpacity>

                    {error && <Text style={styles.error}>{error}</Text>}

                    {/* Android native picker */}
                    {Platform.OS === 'android' && showTimePicker && (
                        <DateTimePicker
                            testID="androidTimePicker"
                            value={displayTime || defaultTime}
                            mode="time"
                            is24Hour={false}
                            onChange={handleAndroidTimeChange}
                            display="spinner"
                            positiveButton={{
                                textColor: Colors.primaryGreen
                            }}
                            maximumDate={maxTime}
                        />
                    )}

                    {/* iOS modal picker */}
                    {Platform.OS === 'ios' && (
                        <Modal
                            visible={showIOSModal}
                            transparent={true}
                            animationType="slide"
                        >
                            <View style={styles.modalContainer}>
                                <View style={styles.modalContent}>
                                    <View style={styles.pickerHeader}>
                                        <TouchableOpacity onPress={handleIOSCancel}>
                                            <Text style={styles.cancelText}>Cancel</Text>
                                        </TouchableOpacity>
                                        <Text style={styles.pickerTitle}>Select Time</Text>
                                        <TouchableOpacity onPress={handleIOSConfirm}>
                                            <Text style={styles.doneText}>Done</Text>
                                        </TouchableOpacity>
                                    </View>

                                    <DateTimePicker
                                        testID="iosTimePicker"
                                        value={pickerTime}
                                        mode="time"
                                        is24Hour={false}
                                        display="spinner"
                                        onChange={handleIOSTimeChange}
                                        style={styles.iosPicker}
                                        maximumDate={maxTime}
                                    />
                                </View>
                            </View>
                        </Modal>
                    )}
                </>
            </SkeletonItem>
        </View>
    );
};

export default CustomTimePickerWithLabel;

const styles = StyleSheet.create({
    inputLabel: {
        padding: 16,
        paddingHorizontal: 24,
        backgroundColor: Colors.primaryGreen,
        borderRadius: 25,
        color: Colors.white,
        fontSize: 19,
        fontFamily: ThemeFonts.Exo_700
    },
    inputBtn: {
        marginTop: 12,
        padding: 7,
        paddingHorizontal: 24,
        backgroundColor: Colors.lightGray,
        borderRadius: 25,
        color: Colors.black,
        fontSize: 18,
        fontFamily: ThemeFonts.Exo_400,
        marginBottom: 12,
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
    },
    error: {
        fontFamily: "Exo_500Medium",
        fontSize: 12,
        color: "red",
        marginLeft: 16,
        position: "absolute",
        bottom: -10,
    },
    // Modal styles
    modalContainer: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
        backgroundColor: 'white',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        paddingBottom: 30,
    },
    pickerHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    pickerTitle: {
        fontSize: 18,
        fontFamily: ThemeFonts.Exo_500,
    },
    cancelText: {
        color: Colors.black,
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_400,
    },
    doneText: {
        color: Colors.primaryGreen,
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_500,
    },
    iosPicker: {
        height: 200,
    }
});