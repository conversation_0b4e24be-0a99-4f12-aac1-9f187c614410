import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Dimensions, ScrollView, BackHandler } from 'react-native';
import YoutubePlayer from 'react-native-youtube-iframe';
import Icon from 'react-native-vector-icons/Ionicons';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';

const { width, height } = Dimensions.get('window');
const videoHeight = Math.floor(height * 0.4); // 40% of screen height

const YouTubePlayerModal = ({
    videoId,
    title,
    description,
    onClose,
    topInset = 0,
}) => {
    useEffect(() => {
        const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
            onClose();
            return true;
        });
        console.log('YouTube Player Modal mounted');
        return () => backHandler.remove();
    }, [onClose]);

    return (
        <View style={[styles.container, { paddingTop: topInset }]}>
            <ScrollView contentContainerStyle={{ paddingBottom: 56 }}>
                <View style={styles.headerContainer}>
                    <TouchableOpacity
                        style={styles.closeButton}
                        onPress={onClose}
                    >
                        <Icon name="close" size={40} color={Colors.white} />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Appetec</Text>
                    <Text style={styles.youtubeLink}>youtube.com</Text>
                </View>

                <View style={styles.contentContainer}>
                    <Text style={styles.title}>
                        {/* {title.toUpperCase()} */}
                        Watch Video
                    </Text>

                    <View style={styles.playerWrapper}>
                        <YoutubePlayer
                            height={videoHeight}
                            play={true}
                            videoId={videoId}
                            onError={(error) => console.log('YouTube Player Error:', error)}
                            onChangeState={(state) => console.log('YouTube Player State:', state)}
                            onReady={() => console.log('YouTube Player Ready')}
                            webViewProps={{
                                androidLayerType: Platform.OS === 'android' ? 'hardware' : undefined,
                                renderToHardwareTextureAndroid: true,
                                androidHardwareAccelerationDisabled: false,
                                style: {
                                    opacity: 0.99,
                                    overflow: 'hidden',
                                }
                            }}
                            initialPlayerParams={{
                                preventFullScreen: false,
                                cc_lang_pref: "us",
                                showClosedCaptions: false,
                                controls: true,
                                modestbranding: true,
                                rel: 0,
                                showinfo: 0,
                                iv_load_policy: 3,
                                fs: 0,
                                playsinline: 1
                            }}
                        />
                    </View>

                    <Text style={styles.description}>{description}</Text>
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        top: 10,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: Colors.white,
        // justifyContent: 'center',
        zIndex: 1000,
    },
    contentContainer: {
        padding: 20,
        width: '100%',
        alignItems: 'center',
    },
    playerWrapper: {
        width: '100%',
        aspectRatio: 16 / 9,
        backgroundColor: Colors.primaryGreen,
        borderRadius: 16,
        overflow: 'hidden',
        elevation: Platform.OS === 'android' ? 1 : 0,
    },
    title: {
        fontSize: 26,
        fontFamily: ThemeFonts.Exo_800,
        color: Colors.black,
        textAlign: 'center',
        marginBottom: 30,
        paddingHorizontal: 10,
        textTransform: 'capitalize',
    },
    description: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.black,
        textAlign: 'left',
        marginTop: 20,
        paddingHorizontal: 20,
        lineHeight: 24,
        textTransform: 'capitalize',
    },
    closeButton: {
        padding: 10,
        zIndex: 1001,
        backgroundColor: 'transparent',
        borderRadius: 2,
        width: 70,
        height: 70,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 10,
        top: 25,
        transform: [{ translateY: -25 }],
    },
    headerContainer: {
        backgroundColor: Colors.darkGray,
        padding: 16,
        flexDirection: 'row',
        alignItems: 'stretch',
        width: '100%',
        height: 70,
    },
    headerTitle: {
        color: Colors.white,
        fontSize: 20,
        fontFamily: ThemeFonts.Exo_600,
        textAlign: 'center',
        flex: 1,
    },
    youtubeLink: {
        color: Colors.white,
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        textAlign: 'center',
        position: 'absolute',
        width: '100%',
        bottom: 3,
        left: 15,
    },
});

export default YouTubePlayerModal;