import React from 'react';
import { Animated, Dimensions, StyleSheet } from 'react-native';
import { screenWidth } from 'constants/sizes';
import { Colors } from 'constants/theme/colors';
import { memo } from 'react';

export const SlidePanel = memo(({ children, visible, side, slideAnim, bottomInset = 0 }) => {
    return (
        <Animated.View
            style={[
                styles.slidePanel,
                {
                    [side]: 0,
                    transform: [{ translateX: slideAnim }],
                    paddingBottom: bottomInset > 0 ? bottomInset : 0, // Add bottom padding for safe area
                },
            ]}
            pointerEvents={visible ? 'auto' : 'none'}
        >
            {children}
        </Animated.View>
    );
});

const styles = StyleSheet.create({
    slidePanel: {
        position: 'absolute',
        top: 100,
        bottom: 0,
        width: screenWidth,
        // marginTop: 50,
        height: '90%',
        backgroundColor: Colors.white,
        zIndex: 20,
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: {
            width: 2,
            height: 0,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        borderTopStartRadius: 50,
        borderTopEndRadius: 50,
    },
    leftPanel: {
        left: 0,
    },
    rightPanel: {
        right: 0,
    },
});
