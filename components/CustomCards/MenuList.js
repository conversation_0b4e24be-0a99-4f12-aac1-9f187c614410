import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, StatusBar, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from 'constants/theme/colors';
import { memo } from 'react';

const menuItems = [
    { name: 'Nutrition', icon: 'nutrition' },
    { name: 'Activity', icon: 'fitness' },
    { name: '<PERSON><PERSON>', icon: 'happy' },
    { name: 'Weight Loss Tracking', icon: 'scale' },
    { name: 'Video Library', icon: 'videocam' },
    { name: 'Sleep', icon: 'moon' },
    { name: 'Device', icon: 'hardware-chip' },
    { name: 'Recipes', icon: 'restaurant' },
    // { name: 'Summary', icon: 'receipt' },
];

export const MenuList = memo(({ onNavigate, bottomPadding = 0 }) => (
    <View style={[menuStyles.menuList, { paddingBottom: bottomPadding }]}>
        <StatusBar />
        <Text style={menuStyles.menuHeader}>Menu</Text>
        <FlatList
            data={menuItems}
            showsHorizontalScrollIndicator={false}
            accessibilityShowsLargeContentViewer={false}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item) => item.name}
            renderItem={({ item }) => (
                <TouchableOpacity
                    activeOpacity={.85}
                    key={item.name}
                    style={menuStyles.menuItem}
                    onPress={() => onNavigate(item.name)}
                >
                    <Text style={menuStyles.menuText}>{item.name}</Text>
                </TouchableOpacity>
            )}
            contentContainerStyle={{ paddingBottom: bottomPadding }}
        />
    </View>
));

const menuStyles = StyleSheet.create({
    menuHeader: {
        fontSize: 28,
        color: Colors.black,
        textAlign: 'start',
        marginHorizontal: 24,
        marginVertical: 10,
        fontFamily: 'Exo_700Bold',
    },
    menuList: {
        width: '100%',
        height: '100%',
        // display: 'flex',
        // flexDirection: 'column',
        // flexWrap: 'wrap',
        // justifyContent: 'center',
        paddingHorizontal: 20,
        paddingVertical: 28,
    },
    menuItem: {
        width: '100%', // slightly less than half to account for spacing
        backgroundColor: Colors.primaryPurple,
        borderRadius: 20,
        padding: 5,
        marginVertical: 6,
        // alignItems: 'center',
        justifyContent: 'center',
        elevation: 3, // for Android shadow
        shadowColor: '#000', // for iOS shadow
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    menuText: {
        // marginTop: 8,
        marginHorizontal: 16,
        marginVertical: 6,
        fontSize: 16,
        fontWeight: '600',
        color: Colors.white,
        textAlign: 'start',
        fontFamily: 'Exo_500Medium',
    }
});
