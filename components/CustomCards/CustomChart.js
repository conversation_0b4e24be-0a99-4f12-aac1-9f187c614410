import React from 'react';
import { Text, View, Dimensions } from 'react-native';
import { LineChart, BarChart } from 'react-native-chart-kit';
import { screenWidth, screenHeight } from 'constants/sizes';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';

const CustomChart = ({ data, chartType, title, chartConfig }) => {
    const ChartComponent = chartType === 'line' ? LineChart : BarChart;

    return (
        <View style={{ marginVertical: 20 }}>
            <Text style={{ fontSize: 18, fontFamily: ThemeFonts.Exo_700, marginBottom: 10, color: Colors.black, paddingHorizontal: 16 }}>
                {title}
            </Text>
            <ChartComponent
                data={data}
                width={screenWidth - 40}
                height={220}
                chartConfig={chartConfig}
                style={{ borderRadius: 16 }}
                bezier={chartType === 'line'}
                showValuesOnTopOfBars={chartType === 'bar'}
            />
        </View>
    );
};

export default CustomChart;
