import React, { useEffect, useState } from 'react';
import {
    StyleSheet,
    View,
    ScrollView,
    TouchableOpacity,
    Image,
    StatusBar,
    Animated,
    Keyboard,
    Dimensions
} from 'react-native';
import { CommonActions, useNavigation, useRoute } from '@react-navigation/native';
import { TouchableWithoutFeedback } from 'react-native';
import { Colors } from 'constants/theme/colors';
import Setting from 'assets/icons/settings.png';
import Logo from 'assets/name_logo.png';
import Lemon from 'assets/illustrator/illustrator_1.png';
import Lemon2 from 'assets/illustrator/illustrator_5.png';
import Leaf from 'assets/illustrator/illustrator_2.png';
import { MenuList } from 'components/CustomCards/MenuList';
import { SettingsList } from 'components/CustomCards/SettingsList';
import { SlidePanel } from 'components/CustomCards/SlidePanel';
import useLayoutStore from 'store/layoutStore';

const screenWidth = Dimensions.get('window').width;
const screenHeight = Dimensions.get('window').height;


// const GlobalScrollViewContext = createContext({
//     scrollViewRef: { current: null },
//     setScrollViewRef: () => { }
// });

// const useGlobalScrollView = () => {
//     const scrollViewRef = useContext(GlobalScrollViewContext);
//     if (!scrollViewRef) return;
//     return scrollViewRef;
// };

const AppLayout = ({ children, illustration, bgColor = Colors.white, paddingHorizontal = 16, paddingTop = 30 }) => {
    const navigation = useNavigation();
    const route = useRoute();
    const { isMenuOpen, toggleMenu: toggleMenuDrawer,
        isSettingOpen, toggleSettings: toggleSettingsDrawer } = useLayoutStore(state => state);

    const [menuSlideAnim] = useState(new Animated.Value(-screenWidth));
    const [settingsSlideAnim] = useState(new Animated.Value(screenWidth));


    const toggleMenu = () => {
        const toValue = isMenuOpen ? 0 : -screenWidth;
        // setMenuVisible(!menuVisible);
        Animated.timing(menuSlideAnim, {
            toValue,
            duration: 300,
            useNativeDriver: true,
        }).start();

        if (isSettingOpen) {
            toggleSettings();
        }
    };

    useEffect(() => {
        toggleMenu();
    }, [isMenuOpen]);


    const toggleSettings = () => {
        const toValue = isSettingOpen ? 0 : screenWidth;
        Animated.timing(settingsSlideAnim, {
            toValue,
            duration: 300,
            useNativeDriver: true,
        }).start();
    };

    useEffect(() => {
        toggleSettings();
    }, [isSettingOpen]);

    const handleOutsidePress = () => {
        if (isMenuOpen) {
            toggleMenu();
            toggleMenuDrawer();
        }
        if (isSettingOpen) {
            toggleSettingsDrawer();
            toggleSettings();
        }
        Keyboard.dismiss();
    };

    return (
        <TouchableWithoutFeedback onPress={handleOutsidePress}>
            <View style={styles.container}>
                {/* Header */}
                <View style={styles.header}>
                    <View style={styles.headerLeft}>
                        <Image source={Logo} style={styles.logo} resizeMode="contain" />
                    </View>
                    <TouchableOpacity style={styles.settingsButton} onPress={toggleSettingsDrawer}>
                        <Image source={Setting} style={styles.settingIcon} resizeMode="contain" />
                    </TouchableOpacity>
                </View>

                {/* Decorative Images */}
                {illustration && (
                    <>
                        {!(isMenuOpen || isSettingOpen) && <View style={styles.lemon} pointerEvents='none'>
                            <Image source={Lemon} style={{ width: "100%", height: "100%" }} resizeMode="contain" />
                        </View>}
                        <View style={styles.lemon2} pointerEvents='none'>
                            <Image source={Lemon2} style={{ width: "100%", height: "100%" }} resizeMode="contain" />
                        </View>
                        <View style={styles.leaf} pointerEvents='none'>
                            <Image source={Leaf} style={{ width: "100%", height: "100%" }} resizeMode="contain" />
                        </View>
                    </>
                )}

                {/* Main Content
                <ScrollViewContext.Provider value={{
                    scrollViewRef,
                    setScrollViewRef: (ref) => {
                        scrollViewRef.current = ref;
                    }
                }}> */}

                <View style={[styles.contentWrapper, { backgroundColor: bgColor, paddingHorizontal: paddingHorizontal, paddingTop: paddingTop }]}>
                    {children}
                </View>
                {/*</ScrollViewContext.Provider> */}

                {/* Overlay for Sliding Panels */}
                {(isMenuOpen || isSettingOpen) && (
                    <TouchableOpacity
                        style={styles.overlay}
                        activeOpacity={1}
                        onPress={handleOutsidePress}
                    />
                )}

                {/* Sliding Panels */}
                <SlidePanel visible={isMenuOpen} side="left" slideAnim={menuSlideAnim}>
                    <MenuList onNavigate={(screen) => {
                        // toggleMenu();
                        toggleMenuDrawer();
                        toggleMenu();
                        navigation.reset({
                            index: 1,
                            routes: [
                                { name: 'dashboard' },
                                { name: screen }
                            ],
                        });
                    }} />
                </SlidePanel>

                <SlidePanel visible={isSettingOpen} side="right" slideAnim={settingsSlideAnim}>
                    <SettingsList onNavigate={(screen) => {
                        toggleSettings();
                        toggleSettingsDrawer();
                        navigation.reset({
                            index: 1,
                            routes: [
                                { name: 'dashboard' },
                                { name: screen }
                            ],
                        });
                    }} />
                </SlidePanel>
            </View>
        </TouchableWithoutFeedback>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.primaryGreen,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 5,
        position: 'absolute',
        top: 20,
        left: 0,
        right: 0,
        height: 90,
        elevation: 5,
    },
    headerLeft: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    logo: {
        width: 120,
        // height: 30,
        marginLeft: 10,
    },
    settingsButton: {
        padding: 10,
    },
    settingIcon: {
        width: 28,
        height: 28,
        tintColor: Colors.white,
    },
    contentWrapper: {
        marginTop: 100,
        backgroundColor: Colors.white,
        borderTopLeftRadius: 50,
        borderTopRightRadius: 50,
        // paddingBottom: 50,
        paddingHorizontal: 20,
        flex: 1,
        // minHeight: screenHeight,
        minWidth: screenWidth,
        overflow: 'hidden'
    },
    lemon: {
        position: 'absolute',
        top: '16%',
        right: '-20%',
        width: '35%',
        height: '35%',
        resizeMode: 'contain',
        zIndex: 10,
        transform: [{ translateY: '-50%' }, { rotate: '95deg' }]
    },
    lemon2: {
        position: 'absolute',
        top: '85%',
        right: '-15%',
        width: '30%',
        height: '30%',
        resizeMode: 'contain',
        zIndex: 10,
        transform: [{ translateY: '-50%' }, { rotate: '-5deg' }]
    },
    leaf: {
        position: 'absolute',
        top: '60%',
        left: '-30%',
        width: '50%',
        height: '50%',
        resizeMode: 'contain',
        zIndex: 5,
        transform: [{ translateY: '-50%' }, { rotate: '35deg' }],
    },
    overlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
});

export default AppLayout;