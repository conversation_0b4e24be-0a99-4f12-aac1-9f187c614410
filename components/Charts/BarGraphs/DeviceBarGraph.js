import {
  processColor,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { memo } from "react";
import { CustomLoader, CustomSelect } from "components/CustomAction";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import { BarChart } from "react-native-charts-wrapper";
import useDeviceTimerStore from "store/deviceTimerStore";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const TIME_FILTER_OPTIONS = [
  { label: "Week", value: "weekly" },
  { label: "Month", value: "monthly" },
  { label: "6 mon", value: "half_yearly" },
  { label: "Year", value: "yearly" },
];

const DeviceBarGraph = memo(({ currentOpenDropdown, setCurrentOpenDropdown, disabled = false }) => {
  const {
    isLoadingDeviceGraphRecords,
    deviceTimeGraphFilter,
    deviceTimeGraphData,
    deviceTimeGraphTimePeriod,
  } = useDeviceTimerStore((state) => state);

  const { getTimerGraphRecords, setDeviceGraphTimeFilter } =
    useDeviceTimerStore((state) => state);

  const handleSelectFilter = async (value) => {
    if (value == deviceTimeGraphFilter) return;

    const prevFilter = deviceTimeGraphFilter;
    setDeviceGraphTimeFilter(value);

    const timerGraphRecordsSuccess = await getTimerGraphRecords(value);

    if (!timerGraphRecordsSuccess) {
      setDeviceGraphTimeFilter(prevFilter);
    }
  };

  const formatMarker = (value) => {
    if (value === 0) return "0sec";

    const hours = Math.floor(value / 3600);
    const minutes = Math.floor((value % 3600) / 60);
    const seconds = value % 60;

    if (hours > 0) {
      return `${hours}hr${minutes > 0 ? ` ${minutes}min` : ''}`;
    } else if (minutes > 0) {
      return `${minutes}min${seconds > 0 ? ` ${seconds}sec` : ''}`;
    } else {
      return `${seconds}sec`;
    }
  };


  if (!deviceTimeGraphData || deviceTimeGraphData.length == 0) return (
    <View style={[styles.graphContainer, { gap: 16 }]}>
      <SkeletonItem height={28} width={"30%"} borderRadius={25} />
      <SkeletonItem height={32} width={"50%"} borderRadius={10} />

      <SkeletonItem height={180} borderRadius={25} colors={[
        'rgba(255, 159, 193, 0.01)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.55)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.01)',
      ]} />
    </View>
  );

  return (
    <TouchableWithoutFeedback
      onPress={() => setCurrentOpenDropdown(null)}
      style={{ flex: 1 }}
    >
      <View style={styles.graphContainer}>
        {isLoadingDeviceGraphRecords && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1000,
              borderRadius: 25,
              backgroundColor: "rgba(0,0,0,0.5)",
            }}
            pointerEvents="box-only"
          >
            <CustomLoader small />
          </View>
        )}
        <View style={{ marginHorizontal: 4 }}>
          <CustomSelect
            options={TIME_FILTER_OPTIONS}
            selectedValue={deviceTimeGraphFilter}
            onValueChange={(value) => handleSelectFilter(value)}
            currentOpenDropdown={currentOpenDropdown}
            dropdownId={"device_graph"}
            setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
            triggerZ={10}
            listZ={9}
            width={140}
            triggerStyle={{
              paddingVertical: 3,
              borderColor: Colors.veryLightGreen,
            }}
            backgroundColor={Colors.lightPurple}
            textColor={Colors.white}
            activeOptionBgColor={Colors.white}
            optionsBgColor={Colors.veryLightGreen}
            optionsBorderWidth={0}
            triggerBorderWidth={1}
            optionsBorderRadius={20}
            alignDropdown="flex-start"
            changeBG={true}
            disabled={disabled}
            disabledBgColor={Colors.lightPurple}
            disabledTextColor={Colors.white}
          />
        </View>
        <View style={styles.graphheader}>
          <Text style={styles.graphHeading}>Device Graph</Text>
          <Text style={styles.graphTimeRange}>
            ({deviceTimeGraphTimePeriod})
          </Text>
        </View>
        <View style={{ flexDirection: "row" }}>
          {/* <View style={{flexDirection:'column',justifyContent:'space-between'}}>
                    <Text style={styles.yAxisText}>24hr</Text>
                    <Text style={styles.yAxisText}>18hr</Text>
                    <Text style={styles.yAxisText}>12hr</Text>
                    <Text style={styles.yAxisText}>6hr</Text>
                    <Text style={styles.yAxisText}>0hr</Text>
                </View> */}
          <View style={{ backgroundColor: Colors.lightPurple, flexGrow: 1 }}>
            <BarChart
              style={{
                flexGrow: 1,
                height: 180,
              }}
              data={{
                dataSets: [
                  {
                    values: deviceTimeGraphData.map((data) => {
                      return { y: data.value / 60, marker: formatMarker(data.value) };
                    }),
                    label: "device_usage_graph",
                    config: {
                      color: processColor(Colors.primaryPurple),
                      valueTextSize: 10,
                      valueTextColor: processColor("transparent"),
                      // valueFormatter: "##0' hr'",
                    },
                  },
                ],
                config: {
                  barWidth: 0.5,
                },
              }}
              xAxis={{
                valueFormatter: deviceTimeGraphData.map((item) => item.label),
                granularityEnabled: true,
                granularity: 1,
                drawAxisLine: false,
                drawGridLines: false,
                fontFamily: ThemeFonts.Exo_500,
                textColor: processColor(Colors.black),
                textSize: 12,
                position: "BOTTOM",
                labelRotationAngle: 0,
                labelCount: deviceTimeGraphData.length,
                avoidFirstLastClipping: false,
                // labelCountForce:true,
                granularity: 1,
                yOffset: 20,
              }}
              animation={{ durationX: 200, durationY: 200 }}
              legend={{
                enabled: false,
              }}
              chartDescription={{
                textColor: processColor("transparent"),
              }}
              marker={{
                enabled: true,
                markerColor: processColor(Colors.primaryGreen),
                textSize: 12,
                textColor: processColor(Colors.white),
              }}
              yAxis={{
                left: {
                  drawAxisLine: false,
                  drawGridLines: false,
                  fontFamily: ThemeFonts.Exo_500,
                  textColor: processColor(Colors.black),
                  textSize: 12,
                  position: "OUTSIDE_CHART",
                  labelCount: 5,
                  avoidFirstLastClipping: true,
                  labelCountForce: true,
                  // valueFormatterPattern: "#.0#",
                  // valueFormatter: "##0' hr'",
                  // valueFormatterPattern:"#.##hr",
                  // granularity:1,
                  // valueFormatter : (value) => {
                  //     return `${value}hr`;
                  // },
                  axisMinimum: 0,
                  axisMaximum: deviceTimeGraphData
                    .map((item) => item.value / 60)
                    .reduce((a, b) => Math.max(a, b)) + 60,
                },
                right: {
                  drawAxisLine: false,
                  drawGridLines: false,
                  fontFamily: ThemeFonts.Exo_500,
                  textColor: processColor(Colors.black),
                  textSize: 12,
                  position: "OUTSIDE_CHART",
                  labelCount: 5,
                  avoidFirstLastClipping: true,
                  labelCountForce: true,
                  // granularity:1,
                  // valueFormatter : (value) => {
                  //     return value;
                  // }
                  axisMinimum: 0,
                  axisMaximum: deviceTimeGraphData
                    .map((item) => item.value / 60)
                    .reduce((a, b) => Math.max(a, b)) + 60,
                },
              }}
              gridBackgroundColor={processColor("#ffffff")}
              visibleRange={{
                x: {
                  min: deviceTimeGraphData.length,
                  max: deviceTimeGraphData.length,
                },
              }}
              drawBarShadow={false}
              drawValueAboveBar={true}
              drawHighlightArrow={true}
              onSelect={() => { }}
              highlights={[]}
              onChange={(event) => { }}
              scaleEnabled={false}
            />
            {/* {
                    containerWidth>0 && <BarChart 
                        data={deviceTimeGraphData}
                        // parentWidth={containerWidth}
                        width={containerWidth-64}
                        barWidth={((containerWidth-64)*.6)/(deviceTimeGraphData.length)}
                        spacing={((containerWidth-64)*.4)/(deviceTimeGraphData.length)}
                        height={140} 
                        frontColor={Colors.primaryPurple}
                        activeOpacity={.85}
                        adjustToWidth
                        hideRules
                        hidedeviceTimeGraphDatas
                        disableScroll
                        // hideYAxisText
                        yAxisLabelSuffix='hr'
                        // hideAxesAndRules
                        focusEnabled
                        // animateOnDataChange
                        // isAnimated
                        backgroundColor={Colors.lightPurple}
                        maxValue={24}
                        noOfSections={4}
                        initialSpacing={8}
                        endSpacing={8}
                        onPress={() => {
                            setCurrentOpenDropdown(null);
                        }}
                        onBackgroundPress={()=>{
                            setCurrentOpenDropdown(null);
                        }}
                        yAxisColor={"transparent"}
                        xAxisColor={"transparent"}
                        yAxisTextStyle={{
                            color: Colors.black,
                            fontSize: 12,
                            fontFamily: ThemeFonts.Exo_500,
                        }}
                        overflowTop={true}
                        secondaryYAxis={{
                            noOfSections: 4,
                            showFractionalValues: true,
                            roundToDigits: 0,
                            yAxisColor: 'transparent',
                            yAxisTextStyle: {
                                color: Colors.black,
                                fontSize: 12,
                                fontFamily: ThemeFonts.Exo_500,
                            },
                            maxValue:12,
                            // yAxisLabelSuffix:""
                            
                        }}
                        // xAxisLabelsVerticalShift={2}
                        xAxisLabelTextStyle={{
                            color: Colors.black,
                            fontSize: 12,
                            fontFamily: ThemeFonts.Exo_500,
                            // paddingTop:4,
                            // backgroundColor:Colors.lightPurple
                        }}
                        // pointerConfig={{
                        //     pointerEvents: 'auto',
                        //     showPointerStrip:false,
                        //     pointerStripColor:Colors.primaryGreen,
                        //     // pointerStripUptodeviceTimeGraphData:true,
                        //     stripOverPointer:true,
                        //     pointerComponent: ({ value,deviceTimeGraphDataText,afterMid }) => {
                        //         const getPosition = () => {
                        //             const pos = {}
                        //             if(afterMid){
                        //                 pos["left"] = -6;
                        //             }else {
                        //                 pos["right"] = 6;
                        //             }
                        //             return pos;
                        //         }
                        //         return (
                        //             <View>
                        //                 <Text style={{ color: Colors.white, ...getPosition() }}>
                        //                     {value}hr
                        //                 </Text>
                        //             </View>
                        //         )
                        //     },
                        // }}
                    />
                } */}
          </View>
          {/* <View style={{flexDirection:'column',justifyContent:'space-between',paddingLeft:8}}>
                    <Text style={styles.yAxisText}>24hr</Text>
                    <Text style={styles.yAxisText}>18hr</Text>
                    <Text style={styles.yAxisText}>12hr</Text>
                    <Text style={styles.yAxisText}>6hr</Text>
                    <Text style={styles.yAxisText}>0hr</Text>
                </View> */}
        </View>
        {/* <View style={{ flexDirection: "row", justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 36 ,marginTop:16}}>
            {
                xAxisLabel.map((item,index) => {
                return <Text style={styles.xAxisText} key={index}>{item}</Text>
                })
            }
            </View> */}
      </View>
    </TouchableWithoutFeedback>
  );
});

export default DeviceBarGraph;

const styles = StyleSheet.create({
  graphContainer: {
    backgroundColor: Colors.lightPurple,
    borderRadius: 30,
    padding: 16,
    paddingHorizontal: 24,
  },
  graphheader: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "baseline",
    gap: 1,
    marginBottom: 10,
    marginTop: 8,
    marginHorizontal: 4,
    flexWrap: "wrap",
  },
  graphHeading: {
    fontSize: 29,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
  },
  graphTimeRange: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.white,
    fontStyle: "italic",
  },
  xAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  yAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
});
