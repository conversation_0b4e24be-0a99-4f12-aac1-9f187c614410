import {
  processColor,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { memo, useEffect, useState } from "react";
import { CustomSelect } from "components/CustomAction";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import { screenWidth } from "constants/sizes";
// import { BarChart } from 'react-native-gifted-charts';
import { Bar, CartesianChart } from "victory-native";
import { useFont } from "@shopify/react-native-skia";
import { Bar<PERSON><PERSON>, LineChart } from "react-native-charts-wrapper";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const TIME_FILTER_OPTIONS = [
  { label: "Week", value: "week" },
  { label: "Month", value: "month" },
  { label: "6 mon", value: "half_yearly" },
  { label: "Year", value: "year" },
];

const weekyData = [
  {
    value: [5, 30, 20, 10],
    label: "M",
  },
  {
    value: [6, 25, 35, 45],
    label: "T",
  },
  {
    value: [6, 10, 15, 20],
    label: "W",
  },
  {
    value: [5, 22, 32, 42],
    label: "T",
  },
  {
    value: [5, 18, 28, 38],
    label: "F",
  },
  {
    value: [5, 16, 26, 36],
    label: "S",
  },
  // {
  //     "value": [10, 20, 30, 40],
  //     "label": "S"
  // },
];

const monthlyData = [
  {
    value: [5, 10, 15, 20],
    label: "W1",
  },
  {
    value: [3, 6, 9, 12],
    label: "W2",
  },
  {
    value: [7, 14, 21, 28],
    label: "W3",
  },
  {
    value: [2, 4, 6, 8],
    label: "W4",
  },
  {
    value: [1, 2, 3, 4],
    label: "W5",
  },
];

const halfYearlyData = [
  {
    value: [8, 16, 24, 32],
    label: "J",
  },
  {
    value: [2, 4, 6, 8],
    label: "F",
  },
  {
    value: [3, 6, 9, 12],
    label: "M",
  },
  {
    value: [4, 8, 12, 16],
    label: "A",
  },
  {
    value: [5, 10, 15, 20],
    label: "M",
  },
  {
    value: [6, 12, 18, 24],
    label: "J",
  },
  {
    value: [7, 14, 21, 28],
    label: "J",
  },
];

const yearlyData = [
  {
    value: [10, 20, 30, 40],
    label: "A",
  },
  {
    value: [5, 10, 15, 20],
    label: "S",
  },
  {
    value: [3, 6, 9, 12],
    label: "O",
  },
  {
    value: [2, 4, 6, 8],
    label: "N",
  },
  {
    value: [1, 2, 3, 4],
    label: "D",
  },
  {
    value: [12, 24, 36, 48],
    label: "J",
  },
  {
    value: [7, 14, 21, 28],
    label: "F",
  },
  {
    value: [6, 12, 18, 24],
    label: "M",
  },
  {
    value: [8, 16, 24, 32],
    label: "A",
  },
  {
    value: [9, 18, 27, 36],
    label: "M",
  },
  {
    value: [11, 22, 33, 44],
    label: "J",
  },
  {
    value: [13, 26, 39, 52],
    label: "J",
  },
];

const ActivityLineGraph = memo(() => {
  const [selectedTimeFilter, setSelectedTimeFilter] = useState("week");
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

  const [dataPoint, setDataPoints] = useState([]);

  useEffect(() => {
    if (selectedTimeFilter == "week") {
      setDataPoints(
        weekyData.map((item, index) => ({
          value: item.value,
          dataPointText: item.value,
          afterMid: index > weekyData.length / 2,
          label: item.label,
        }))
      );
    } else if (selectedTimeFilter == "month") {
      setDataPoints(
        monthlyData.map((item, index) => ({
          value: item.value,
          dataPointText: item.value,
          afterMid: index > monthlyData.length / 2,
          label: item.label,
        }))
      );
    } else if (selectedTimeFilter == "half_yearly") {
      setDataPoints(
        halfYearlyData.map((item, index) => ({
          value: item.value,
          dataPointText: item.value,
          afterMid: index > halfYearlyData.length / 2,
          label: item.label,
        }))
      );
    } else if (selectedTimeFilter == "year") {
      setDataPoints(
        yearlyData.map((item, index) => ({
          value: item.value,
          dataPointText: item.value,
          afterMid: index > yearlyData.length / 2,
          label: item.label,
        }))
      );
    }
  }, [selectedTimeFilter]);

  if (!dataPoint || dataPoint.length == 0) return (
    <View style={[styles.graphContainer, { gap: 16 }]}>
      <SkeletonItem height={28} width={"30%"} borderRadius={25} />
      <SkeletonItem height={32} width={"50%"} borderRadius={10} />

      <SkeletonItem height={180} borderRadius={25} colors={[
        'rgba(255, 159, 193, 0.01)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.55)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.01)',
      ]} />
    </View>
  );

  return (
    <TouchableWithoutFeedback
      onPress={() => setCurrentOpenDropdown(null)}
      style={{ flex: 1 }}
    >
      <View style={styles.graphContainer}>
        <View style={{ marginHorizontal: 4 }}>
          <CustomSelect
            options={TIME_FILTER_OPTIONS}
            selectedValue={selectedTimeFilter}
            onValueChange={(value) => setSelectedTimeFilter(value)}
            currentOpenDropdown={currentOpenDropdown}
            dropdownId={1}
            setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
            triggerZ={10}
            listZ={9}
            width={140}
            triggerStyle={{
              paddingVertical: 3,
              borderColor: Colors.veryLightGreen,
            }}
            backgroundColor={Colors.lightPurple}
            textColor={Colors.white}
            activeOptionBgColor={Colors.white}
            optionsBgColor={Colors.veryLightGreen}
            optionsBorderWidth={0}
            triggerBorderWidth={1}
            optionsBorderRadius={20}
            alignDropdown="flex-start"
            changeBG={true}
          />
        </View>
        <View style={styles.graphheader}>
          <Text style={styles.graphHeading}>Activity Graph</Text>
          <Text style={styles.graphTimeRange}>(1 Jan - 7 Jan 2025)</Text>
        </View>
        <View style={{ backgroundColor: Colors.lightPurple, flexGrow: 1 }}>
          <LineChart
            style={{
              flexGrow: 1,
              height: 180,
            }}
            data={{
              dataSets: [
                {
                  values: dataPoint.map((data) => {
                    return {
                      y: data.value[0] * 100,
                      marker: `${data.value[0]}kcal`,
                    };
                  }),
                  label: "Recorded average weight",
                  config: {
                    circleColor: processColor("transparent"),
                    circleHoleColor: processColor("transparent"),
                    valueTextColor: processColor("transparent"),
                    lineWidth: 2,
                    color: processColor(Colors.red),
                    mode: "HORIZONTAL_BEZIER",
                    highlightColor: processColor(Colors.lightGreen),
                  },
                },
              ],
            }}
            chartDescription={{ text: "" }}
            legend={{
              enabled: false,
            }}
            animation={{ durationX: 250, durationY: 0, easingX: "EaseInQuad" }}
            marker={{
              enabled: true,
              markerColor: processColor(Colors.primaryGreen),
              textSize: 12,
              textColor: processColor(Colors.white),
            }}
            extraOffsets={{
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
            }}
            xAxis={{
              valueFormatter: dataPoint.map((item) => item.label),
              granularityEnabled: true,
              granularity: 1,
              drawAxisLine: false,
              drawGridLines: false,
              fontFamily: ThemeFonts.Exo_500,
              textColor: processColor(Colors.black),
              textSize: 12,
              position: "BOTTOM",
              labelRotationAngle: 0,
              labelCount: dataPoint.length,
              avoidFirstLastClipping: true,
              // labelCountForce:true,
              granularity: 1,
              yOffset: 28,
            }}
            yAxis={{
              left: {
                drawAxisLine: false,
                drawGridLines: false,
                fontFamily: ThemeFonts.Exo_500,
                textColor: processColor(Colors.black),
                textSize: 12,
                position: "OUTSIDE_CHART",
                labelCount: 5,
                avoidFirstLastClipping: true,
                labelCountForce: true,
                // valueFormatter: "##0' kg'",
                axisMinimum: 0,
                axisMaximum:
                  dataPoint
                    .map((item) => item.value[0] * 100)
                    .reduce((a, b) => Math.max(a, b)) + 200,
              },
              right: {
                drawAxisLine: false,
                drawGridLines: false,
                fontFamily: ThemeFonts.Exo_500,
                textColor: processColor(Colors.black),
                textSize: 12,
                position: "OUTSIDE_CHART",
                labelCount: 5,
                avoidFirstLastClipping: true,
                labelCountForce: true,
                // valueFormatter: "##0' kg'",
                axisMinimum: 0,
                // axisMaximum:24
              },
            }}
            drawGridBackground={false}
            drawBorders={false}
            touchEnabled={true}
            scaleEnabled={false}
            onSelect={() => { }}
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
});

export default ActivityLineGraph;

const styles = StyleSheet.create({
  graphContainer: {
    backgroundColor: Colors.lightPurple,
    borderRadius: 30,
    padding: 16,
    paddingHorizontal: 24,
  },
  graphheader: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "baseline",
    gap: 1,
    marginBottom: 10,
    marginTop: 8,
    marginHorizontal: 4,
    flexWrap: "wrap",
  },
  graphHeading: {
    fontSize: 29,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
  },
  graphTimeRange: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.white,
    fontStyle: "italic",
  },
  xAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  yAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  legendWrapper: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 16,
    justifyContent: "space-between",
    // backgroundColor:'red',
    marginHorizontal: 12,
    marginTop: 16,
  },
  legendContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
  },
  legendColor: {
    width: 10,
    height: 10,
    borderRadius: 25,
    backgroundColor: Colors.primaryPurple,
  },
  legendText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
});
