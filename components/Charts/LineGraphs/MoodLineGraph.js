import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useEffect, useState } from "react";
import { CustomLoader, CustomSelect } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import { LineChart } from "react-native-gifted-charts";
import { ThemeFonts } from "constants/theme/fonts";

import useMoodStore from "store/moodStore";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const TIME_FILTER_OPTIONS = [
  { label: "Week", value: "weekly" },
  { label: "Month", value: "monthly" },
  { label: "6 mon", value: "half_yearly" },
  { label: "Year", value: "yearly" },
];

const convertToYAxisValue = (value) => {
  switch (value) {
    case "happy":
      return 0;
    case "moderately happy":
      return 1;
    case "irritated":
      return 2;
    case "anxious":
      return 3;
    case "sad":
      return 4;
    default:
      return 0;
  }
};

const MoodLineGraph = ({ currentOpenDropdown, setCurrentOpenDropdown }) => {
  const {
    isLoadingMoodGraphData,
    moodGraphData,
    moodGraphTimeRange,
    moodGraphFilter,
  } = useMoodStore((state) => state);
  const { getMoodGraphData, setMoodGraphFilter } = useMoodStore(
    (state) => state
  );
  const [containerWidth, setContainerWidth] = useState(0);

  const handleSelectFilter = async (value) => {
    if (value == moodGraphFilter) return;

    const prevFilter = moodGraphFilter;

    setMoodGraphFilter(value);

    const getMoodGraphDataSuccess = await getMoodGraphData({ filter: value });

    if (!getMoodGraphDataSuccess) {
      setMoodGraphFilter(prevFilter);
    }
  };

  if (!moodGraphData || moodGraphData.length == 0) return (
    <View style={[styles.graphContainer, { gap: 16 }]}>
      <SkeletonItem height={28} width={"30%"} borderRadius={25} />
      <SkeletonItem height={32} width={"50%"} borderRadius={10} />

      <SkeletonItem height={180} borderRadius={25} colors={[
        'rgba(255, 159, 193, 0.01)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.55)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.01)',
      ]} />
    </View>
  );

  return (
    <TouchableWithoutFeedback
      onPress={() => {
        setCurrentOpenDropdown(null);
      }}
      style={{ flex: 1 }}
    >
      <View style={styles.graphContainer}>
        {isLoadingMoodGraphData && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1000,
              borderRadius: 25,
              backgroundColor: "rgba(0,0,0,0.5)",
            }}
            pointerEvents="box-only"
          >
            <CustomLoader small />
          </View>
        )}
        <View style={{ marginHorizontal: 4 }}>
          <CustomSelect
            options={TIME_FILTER_OPTIONS}
            selectedValue={moodGraphFilter}
            onValueChange={(value) => handleSelectFilter(value)}
            currentOpenDropdown={currentOpenDropdown}
            dropdownId={"mood_graph"}
            setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
            triggerZ={10}
            listZ={9}
            width={140}
            triggerStyle={{
              paddingVertical: 3,
              borderColor: Colors.veryLightGreen,
            }}
            backgroundColor={Colors.lightPurple}
            textColor={Colors.white}
            activeOptionBgColor={Colors.white}
            optionsBgColor={Colors.veryLightGreen}
            optionsBorderWidth={0}
            triggerBorderWidth={1}
            optionsBorderRadius={20}
            alignDropdown="flex-start"
            disabled={isLoadingMoodGraphData}
            changeBG={true}
            disabledBgColor={Colors.lightPurple}
            disabledTextColor={Colors.white}
          />
        </View>
        <View style={styles.graphheader}>
          <Text style={styles.graphHeading}>Mood Graph</Text>
          <Text style={styles.graphTimeRange}>{`(${moodGraphTimeRange || ""
            })`}</Text>
        </View>
        <View
          style={{
            paddingHorizontal: 0,
            justifyContent: "center",
            flexDirection: "row",
            flexWrap: "nowrap",
            marginBottom: 16,
          }}
        >
          <View
            style={{
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 0,
            }}
          >
            <Image
              source={require("assets/icons/moods/angry.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/sad.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/neutral.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/moderate_happy.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/happy.png")}
              style={{ width: 24, height: 24 }}
            />
          </View>
          <View
            style={{
              backgroundColor: Colors.lightPurple,
              flex: 1,
              height: 140,
              marginBottom: 16,
            }}
            onLayout={(event) => {
              const { width } = event.nativeEvent.layout;
              setContainerWidth(width);
            }}
          >
            {containerWidth > 0 && (
              <LineChart
                data={moodGraphData.map((item, index) => {
                  return {
                    value: convertToYAxisValue(item.value),
                    dataPointText: item?.value ? item.value : "Happy",
                    afterMid: index > moodGraphData.length / 2,
                  };
                })}
                width={containerWidth}
                color={Colors.red}
                thickness={2.1}
                isAnimated
                hideRules
                hideDataPoints
                hideYAxisText
                adjustToWidth
                focusEnabled
                animateOnDataChange
                height={140}
                maxValue={4}
                stepValue={1}
                backgroundColor={Colors.lightPurple}
                hideAxesAndRules={true}
                // spacing={10}
                // xAxisLength	={containerWidth}
                onFocus={() => {
                  setCurrentOpenDropdown(null);
                }}
                initialSpacing={0}
                endSpacing={0}
                pointerConfig={{
                  pointerEvents: "auto",
                  showPointerStrip: false,
                  pointerStripColor: Colors.primaryGreen,
                  // pointerStripUptoDataPoint:true,
                  stripOverPointer: true,
                  pointerComponent: ({ value, dataPointText, afterMid }) => {
                    const getPosition = () => {
                      const pos = {};
                      if (afterMid) {
                        pos["left"] = "-100%";
                      } else {
                        pos["right"] = -4;
                      }
                      return pos;
                    };
                    return (
                      <View>
                        {/* <View style={{width:8,height:8,backgroundColor:Colors.darkGreen,borderRadius:25}}></View> */}
                        <Text style={{ color: Colors.white, ...getPosition() }}>
                          {dataPointText.substr(0, 1).toUpperCase() +
                            dataPointText.substr(1)}
                        </Text>
                      </View>
                    );
                  },
                }}
              />
            )}
          </View>
          <View
            style={{
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 0,
              marginLeft: 20,
            }}
          >
            <Image
              source={require("assets/icons/moods/angry.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/sad.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/neutral.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/moderate_happy.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/happy.png")}
              style={{ width: 24, height: 24 }}
            />
          </View>
        </View>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            paddingHorizontal: 24,
          }}
        >
          {moodGraphData.map((item, index) => {
            return (
              <Text style={styles.xAxisText} key={index}>
                {item.label}
              </Text>
            );
          })}
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default MoodLineGraph;

const styles = StyleSheet.create({
  graphContainer: {
    backgroundColor: Colors.lightPurple,
    borderRadius: 30,
    padding: 16,
    paddingHorizontal: 24,
  },
  graphheader: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "baseline",
    gap: 1,
    marginBottom: 10,
    marginTop: 8,
    marginHorizontal: 4,
  },
  graphHeading: {
    fontSize: 29,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
  },
  graphTimeRange: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.white,
    fontStyle: "italic",
  },
  xAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
});

const getMoodName = (value) => {
  switch (value) {
    case 0:
      return "Happy";
    case 1:
      return "Moderately Happy";
    case 2:
      return "Irritated";
    case 3:
      return "Anxious";
    case 4:
      return "Sad";
    default:
      return "Happy";
  }
};
