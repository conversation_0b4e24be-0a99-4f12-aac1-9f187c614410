import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Linking,
} from "react-native";
import { Colors } from "constants/theme/colors";
import Icon from "react-native-vector-icons/Ionicons";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { screenWidth } from "constants/sizes";
import { WebView } from "react-native-webview";
import * as WebBrowser from "expo-web-browser";
import { useSafeAreaInsets } from "react-native-safe-area-context";

export const DeviceManagementScreen = () => {
  const [devices, setDevices] = useState([
    {
      id: 1,
      name: "Mark v01",
      status: "Connected",
      lastSync: "2024-02-11 14:30",
      batteryLevel: "75%",
    },
  ]);

  const [showVideo, setShowVideo] = useState(false);
  const insets = useSafeAreaInsets();
  const topSafeAreaHeight = insets.top;

  const toggleDeviceConnection = (id) => {
    setDevices((prevDevices) =>
      prevDevices.map((device) =>
        device.id === id
          ? {
            ...device,
            status:
              device.status === "Connected" ? "Disconnected" : "Connected",
          }
          : device
      )
    );
  };

  const handleShowTutorial = async () => {
    // setShowVideo(true);
    await WebBrowser.openBrowserAsync(
      "https://www.youtube.com/watch?video_tutorial",
      {
        showInRecents: false,
      }
    );
  };

  // if (showVideo) {
  //     return <View style={{ flex: 1, paddingTop: topSafeAreaHeight, backgroundColor: Colors.primaryGreen }}>
  //         <WebView
  //             source={{ uri: 'https://www.youtube.com/watch?video_tutorial' }}
  //             style={{ flex: 1 }}
  //         />
  //     </View>
  // }

  return (
    <AppLayout illustration={false}>
      <ScrollView style={styles.container}>
        <Text style={styles.menuHeader}>Device</Text>

        {devices.map((device) => (
          <View key={device.id} style={styles.deviceCard}>
            <View style={styles.deviceHeader}>
              <Text style={styles.deviceName}>{device.name}</Text>
              <TouchableOpacity
                style={[
                  styles.statusButton,
                  {
                    backgroundColor:
                      device.status === "Connected"
                        ? Colors.primaryGreen
                        : Colors.textLight,
                  },
                ]}
                onPress={() => toggleDeviceConnection(device.id)}
              >
                <Text style={styles.statusText}>{device.status}</Text>
              </TouchableOpacity>
            </View>

            {/* <View style={styles.deviceInfo}>
                            <View style={styles.infoItem}>
                                <Icon name="time-outline" size={20} color={Colors.textDark} />
                                <Text style={styles.infoText}>Last Sync: {device.lastSync}</Text>
                            </View>
                            <View style={styles.infoItem}>
                                <Icon name="battery-half-outline" size={20} color={Colors.textDark} />
                                <Text style={styles.infoText}>Battery: {device.batteryLevel}</Text>
                            </View>
                        </View> */}
          </View>
        ))}

        <View style={styles.helpCard}>
          <Text style={styles.helpHeader}>How to connect a device?</Text>
          <View style={styles.videoContainer}></View>

          <TouchableOpacity
            style={styles.videoCard}
            onPress={() => handleShowTutorial()}
          >
            <Icon name="play-circle-outline" size={40} color={Colors.white} />
            <Text style={styles.videoText}>Watch Tutorial Video</Text>
          </TouchableOpacity>
        </View>
        <Text style={styles.helpText}>
          Need help connecting a device? Check our device compatibility guide or
          contact support.
        </Text>
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    width: screenWidth,
    left: -20,
    backgroundColor: Colors.white,
    padding: 20,
    borderRadius: 10,
    zIndex: 100,
  },
  menuHeader: {
    fontSize: 28,
    color: Colors.black,
    textAlign: "left",
    marginLeft: 20,
    marginBottom: 15,
    fontFamily: "Exo_700Bold",
  },
  deviceCard: {
    backgroundColor: Colors.white,
    padding: 15,
    // borderRadius: 15,
    borderRadius: 24,
    marginBottom: 15,
    borderColor: Colors.primaryPurple,
    borderWidth: 2,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    elevation: 2,
  },
  deviceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    // marginBottom: 10,
  },
  deviceName: {
    fontSize: 18,
    fontFamily: "Exo_400Regular",
    color: Colors.textDark,
  },
  statusButton: {
    paddingHorizontal: 14,
    paddingVertical: 7,
    borderRadius: 20,
  },
  statusText: {
    color: Colors.white,
    fontSize: 13,
    fontWeight: "bold",
  },
  deviceInfo: {
    marginTop: 5,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 5,
  },
  infoText: {
    marginLeft: 8,
    color: Colors.textDark,
    fontSize: 14,
  },
  helpCard: {
    height: 250,
    backgroundColor: Colors.lightPurple,
    padding: 20,
    borderRadius: 24,
    marginTop: 20,
  },
  helpHeader: {
    fontSize: 30,
    color: Colors.white,
    fontFamily: "Exo_700Bold",
    marginBottom: 5,
  },
  helpText: {
    color: Colors.white,
    fontSize: 14,
    fontFamily: "Exo_400Regular",
    marginBottom: 10,
  },
});

export default DeviceManagementScreen;
