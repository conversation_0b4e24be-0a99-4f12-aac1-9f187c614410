import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { CustomButton } from 'components/CustomAction'
import { Colors } from 'constants/theme/colors'
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { reminderService } from 'services/remindersService';
import { ThemeFonts } from 'constants/theme/fonts';

const ReminderOverviewCard = ({ reminder, onRefresh, setReminderError, isActive, setMessage, setIsFetching }) => {
    const navigation = useNavigation();

    const deleteReminder = async () => {
        setIsFetching(true);
        const response = await reminderService.deleteRemainder(reminder.id);

        if (!response.success) {
            setReminderError(response.error);
        }

        setMessage("Reminder deleted successfully.");
        setIsFetching(false);
        await onRefresh();
    }

    return (
        <View key={reminder.id} style={[styles.reminderCard, { opacity: isActive ? 1 : 0.7 }]}>
            <View style={styles.reminderContent}>
                <View style={styles.reminderHeader}>
                    <Text style={styles.reminderTitle}>{reminder.label}</Text>
                    <Text style={styles.reminderTime}>
                        {String(reminder.userTime.hour).padStart(2, 0)}:{String(reminder.userTime.minute).padStart(2, 0)} {reminder.userTime.period}
                    </Text>
                </View>
                <CustomButton
                    title="Edit"
                    onPress={isActive ? () => navigation.navigate("Edit Reminder", { id: reminder.id }) : () => { }}
                    style={styles.editButton}
                    fontFamily="Exo_600SemiBold"
                />
                {/* <TouchableOpacity
                    activeOpacity={.8}
                    style={styles.deleteButton}
                    onPress={isActive ? deleteReminder : () => { }}
                >
                    <Icon name="trash-outline" size={18} color={"red"} />
                </TouchableOpacity> */}
            </View>
        </View>
    )
}

export default ReminderOverviewCard

const styles = StyleSheet.create({
    reminderCard: {
        backgroundColor: Colors.primaryGreen,
        borderRadius: 24,
        padding: 18,
        paddingHorizontal: 25,
        // marginVertical: 16,
        // marginBottom: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    reminderContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
    },
    reminderHeader: {
        flex: 1,
    },
    reminderTitle: {
        fontSize: 19,
        color: Colors.lightGreen,
        fontFamily: 'Exo_700Bold',
        marginBottom: 2,
        textTransform: "capitalize"
    },
    reminderTime: {
        fontSize: 30,
        fontFamily: ThemeFonts.Lexend_400,
        color: Colors.white,
    },
    editButton: {
        // marginLeft: 10,
        maxWidth: 72
    },
    deleteButton: {
        position: 'absolute',
        top: -10,
        right: 0,
        // backgroundColor: Colors.white,
        padding: 8
    },
})