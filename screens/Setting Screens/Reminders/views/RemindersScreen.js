import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  TouchableWithoutFeedback,
  Animated,
  FlatList,
  RefreshControl,
  Platform,
} from "react-native";
import {
  CustomAlert,
  CustomLoader,
  CustomSelect,
  CustomToggleButton,
} from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import Icon from "react-native-vector-icons/Ionicons";
import { screenWidth } from "constants/sizes";
import ReminderOverviewCard from "../components/ReminderOverviewCard";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { CATEGORIES } from "../../../../constants/constants";
import { reminderService } from "services/remindersService";
import { useAuth } from "context/AuthContext";
import FlatListBottomLoader from "components/Loaders/FlatListBottomLoader";
import {
  getDeviceType,
  togglePushNotificationActivation,
} from "services/pushNotificationRegistrationService";
import OpenSettingsPopup from "components/PopUps/OpenSettingsPopup";
import DeviceInfo from "react-native-device-info";
import * as Device from "expo-device";
import apiClient from "services/axiosInstance";
import useNotificationStore from "store/notificationStore";
import AppLayout from "navigations/components/Layouts/AppLayout";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

export const RemindersScreen = () => {
  const navigation = useNavigation();

  const [selectedCategory, setSelectedCategory] = useState(CATEGORIES[0].value);

  const { isLoadingNotifications, isNotificationEnabled, notificationError } =
    useNotificationStore((state) => state);

  const { setIsLoadingNotifications, toggleNotifications, clearError } =
    useNotificationStore((state) => state);

  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

  const [reminderError, setReminderError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [showNotificationPermissionModel, setShowNotificationPermissionModel] =
    useState(false);
  const [message, setMessage] = useState(null);

  const [reminders, setReminders] = useState([]);

  const getRemindersData = async () => {
    if (isFetching || !hasMore) return;

    setIsFetching(true);

    const response = await reminderService.getReminders(selectedCategory, page);

    if (response.success) {
      if (response.data.length > 0) {
        setReminders((prevData) => [...prevData, ...response.data]);
        setPage((prevPage) => prevPage + 1);
      } else {
        setHasMore(false);
      }
    } else {
      setReminderError(response.error);
      setHasMore(false);
    }

    setIsFetching(false);
  };

  const handleLoadMoreRemainders = () => {
    if (!isFetching && hasMore) {
      getRemindersData();
    }
  };

  const onRefresh = async () => {
    setIsFetching(true);
    setPage(1);
    setHasMore(true);
    setReminders([]);

    const response = await reminderService.getReminders(selectedCategory, 1);

    if (response.success) {
      setReminders(response.data);
      setPage(2);
      setHasMore(response.data.length > 0);
    } else {
      setReminderError(response.error);
      setHasMore(false);
    }

    setIsFetching(false);
  };

  useFocusEffect(
    React.useCallback(() => {
      onRefresh();
    }, [selectedCategory])
  );

  useEffect(() => {
    let isActive = true;
    setIsFetching(true);
    setHasMore(true);
    setPage(1);
    setReminders([]);

    (async () => {
      const response = await reminderService.getReminders(selectedCategory, 1);

      if (isActive) {
        if (response.success) {
          setReminders(response.data);
          setPage(2);
          setHasMore(response.data.length > 0);
        } else {
          setReminderError(response.error);
          setHasMore(false);
        }
        setIsFetching(false);
      }
    })();

    return () => {
      isActive = false;
    };
  }, [selectedCategory]);

  const changeNotificationStatus = async () => {
    try {
      if (!isNotificationEnabled) {
        setIsLoadingNotifications(true);
        const pushTokenRes = await togglePushNotificationActivation();

        if (!pushTokenRes.success) {
          if (
            pushTokenRes.error ==
            "Grant push notification to never miss an update."
          ) {
            setShowNotificationPermissionModel(true);
          } else {
            setReminderError(pushTokenRes.error);
          }
          setIsLoadingNotifications(false);
          return;
        }

        const deviceId = await DeviceInfo.getUniqueId();

        const deviceData = {
          notificationToken: pushTokenRes.pushTokenString,
          deviceId: deviceId,
          deviceType: getDeviceType(Device.deviceType),
          osType: Platform.OS,
        };

        const res = await apiClient.post(`/notification_token`, deviceData);
      }
      await toggleNotifications();
    } catch (error) {
      setReminderError("An error occurred while changing reminder status.");
      setIsLoadingNotifications(false);
    }
  };

  if (isLoadingNotifications || isLoading) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
        <View style={{ flex: 1, flexDirection: "column", marginBottom: 70 }}>
          {showNotificationPermissionModel && (
            <OpenSettingsPopup
              visible={showNotificationPermissionModel}
              title={"Notification Permission"}
              message={"Grant push notification to never miss an update."}
              onClose={() => setShowNotificationPermissionModel(false)}
            />
          )}
          <ScrollView
            style={styles.container}
            contentContainerStyle={{ flexGrow: 1, paddingBottom: 45 }}
            showsVerticalScrollIndicator={false}
            onScroll={(e) => {
              let paddingToBottom = 45;
              paddingToBottom += e.nativeEvent.layoutMeasurement.height;
              if (
                e.nativeEvent.contentOffset.y >=
                e.nativeEvent.contentSize.height - paddingToBottom
              ) {
                handleLoadMoreRemainders();
              }
            }}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={() => {
                  setRefreshing(true);
                  onRefresh();
                  setRefreshing(false);
                }}
                colors={[Colors.primaryGreen]}
                progressViewOffset={24}
              />
            }
          >
            <CustomAlert
              visible={!!notificationError || !!reminderError}
              title={"Error"}
              message={notificationError || reminderError}
              buttons={[
                { text: "OK", onPress: () => { }, style: "allowButton" },
              ]}
              onClose={() => {
                clearError();
                setReminderError(null);
              }}
            />
            <CustomAlert
              visible={!!message}
              title={"Success"}
              message={message}
              buttons={[
                { text: "OK", onPress: () => { }, style: "allowButton" },
              ]}
              onClose={() => {
                setMessage(null);
              }}
            />
            <TouchableWithoutFeedback
              onPress={() => setCurrentOpenDropdown(null)}
            >
              <View
                style={{
                  flex: 1,
                }}
              >
                <View style={styles.header}>
                  <Text style={styles.headerText}>Reminders</Text>
                  <CustomToggleButton
                    checked={isNotificationEnabled}
                    onToggle={changeNotificationStatus}
                    disabled={isLoadingNotifications}
                  />
                </View>

                <View style={styles.notificationsContainer}>
                  <CustomSelect
                    containerStyle={styles.categoryButton}
                    options={CATEGORIES}
                    selectedValue={selectedCategory}
                    onValueChange={(value) => setSelectedCategory(value)}
                    label="Category"
                    backgroundColor={Colors.primaryPurple}
                    currentOpenDropdown={currentOpenDropdown}
                    textColor={Colors.white}
                    dropdownId={1}
                    setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                    changeBG={true}
                  />
                  {reminders.length === 0 && !isFetching ? (
                    <View style={styles.emptyContainer}>
                      <Text style={styles.emptyText}>No reminders found</Text>
                    </View>
                  ) : (
                    <FlatList
                      data={reminders}
                      keyExtractor={(item) => item.id.toString()}
                      scrollEnabled={false}
                      contentContainerStyle={{ gap: 16, marginTop: 16 }}
                      renderItem={({ item, index }) => (
                        <ReminderOverviewCard
                          reminder={item}
                          onRefresh={onRefresh}
                          setIsFetching={setIsLoading}
                          setReminderError={(error) => {
                            setReminderError(error);
                          }}
                          isActive={isNotificationEnabled}
                          setMessage={setMessage}
                        />
                      )}
                      ListFooterComponent={
                        isFetching && hasMore ? (
                          reminders.length > 0 ? (
                            <FlatListBottomLoader />
                          ) : (
                            <View style={{ gap: 16, marginTop: 16 }}>
                              <SkeletonItem height={100} borderRadius={25} />
                              <SkeletonItem height={100} borderRadius={25} />
                              <SkeletonItem height={100} borderRadius={25} />
                            </View>
                          )
                        ) : null
                      }
                    />
                  )}
                </View>
              </View>
            </TouchableWithoutFeedback>
          </ScrollView>
          <TouchableOpacity
            activeOpacity={0.5}
            style={styles.addRemainderContainer}
            onPress={() => {
              setCurrentOpenDropdown(null);
              navigation.navigate("Add Reminder");
            }}
          >
            <Icon
              name="add-circle-sharp"
              style={styles.addRemainderIcon}
              size={24}
            />
            <Text style={styles.addRemainderText}>Add a Reminder</Text>
          </TouchableOpacity>
        </View>
      </TouchableWithoutFeedback>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    width: screenWidth,
    left: -20,
    backgroundColor: Colors.white,
    padding: 20,
    borderRadius: 10,
    // zIndex: 100,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    marginHorizontal: 16,
  },
  headerText: {
    fontSize: 32,
    color: Colors.black,
    textAlign: "start",
    fontFamily: "Exo_700Bold",
  },
  categoryContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 20,
  },
  categoryButton: {
    flex: 1,
    minWidth: "30%",
    backgroundColor: Colors.lightPurple,
  },
  notificationsContainer: {
    flex: 1,
  },
  addRemainderContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    paddingVertical: 8,
    gap: 5,
  },
  addRemainderIcon: {
    color: Colors.primaryPurple,
  },
  addRemainderText: {
    fontSize: 16,
    fontFamily: "Exo_500Medium",
    color: Colors.primaryPurple,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyText: {
    fontSize: 24,
    fontFamily: "Exo_600SemiBold",
    color: Colors.primaryPurple,
  },
});
