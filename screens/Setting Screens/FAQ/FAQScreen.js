import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Pressable,
  FlatList,
} from "react-native";
import { CustomInput, CustomSearch } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { screenHeight, screenWidth } from "constants/sizes";
import FAQ_HelpService from "services/FAQ_HelpService";
import FAQCard from "./components/FAQCard";
import FlatListBottomLoader from "components/Loaders/FlatListBottomLoader";

export const FAQScreen = () => {
  const [searchQuery, setSearchQuery] = useState("");

  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [FAQs, setFAQs] = useState([]);

  const [expandedQuestion, setExpandedQuestion] = useState(null);

  useEffect(() => {
    let isMounted = true;
    setLoading(true);
    setPage(1);

    const fetchData = async () => {
      try {
        const response = await FAQ_HelpService.getAllFAQs({
          page: 1,
          searchQuery,
        });

        if (isMounted) {
          setFAQs(response.data);
          setPage(2);
          setHasMore(response.data.length > 0);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        if (isMounted) {
          setFAQs([]);
          setHasMore(false);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [searchQuery]);

  const getFAQs = async () => {
    if (loading || !hasMore) return;
    setLoading(true);

    try {
      const response = await FAQ_HelpService.getAllFAQs({
        page: page,
        searchQuery,
      });

      if (response.data.length > 0) {
        setFAQs((prevData) => [...prevData, ...response.data]);
        setPage((prevPage) => prevPage + 1);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error fetching more data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      getFAQs();
    }
  };

  return (
    <AppLayout illustration={false}>
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        contentContainerStyle={[
          hasMore ? { paddingBottom: 150 } : { paddingBottom: 90 },
        ]}
        onScroll={(e) => {
          let paddingToBottom = 10;
          paddingToBottom += e.nativeEvent.layoutMeasurement.height;
          if (
            e.nativeEvent.contentOffset.y >=
            e.nativeEvent.contentSize.height - paddingToBottom
          ) {
            handleLoadMore();
          }
        }}
      >
        <Text style={styles.headerText}>FAQs</Text>

        <CustomSearch
          value={searchQuery}
          onChangeText={(value) => setSearchQuery(value)}
          placeholder="Search For FAQs"
        />

        {FAQs.length === 0 ? (
          <Text
            style={{
              textAlign: "center",
              marginTop: 20,
              color: Colors.black,
              fontSize: 16,
              fontFamily: "Exo_400Regular",
            }}
          >
            No FAQs Found
          </Text>
        ) : (
          <FlatList
            data={FAQs}
            keyExtractor={(item) => item.id.toString()}
            scrollEnabled={false}
            renderItem={({ item, index }) => (
              <FAQCard
                index={index}
                id={item.id}
                question={item.question}
                answer={item.answer}
                expandedQuestion={expandedQuestion}
                setExpandedQuestion={setExpandedQuestion}
              />
            )}
            ListFooterComponent={
              loading && hasMore ? <FlatListBottomLoader /> : null
            }
          />
        )}
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    width: screenWidth,
    backgroundColor: Colors.white,
    padding: 20,
    left: -20,
    borderRadius: 15,
  },
  headerText: {
    fontSize: 32,
    color: Colors.black,
    textAlign: "start",
    marginHorizontal: 24,
    top: -10,
    marginBottom: 20,
    fontFamily: "Exo_700Bold",
  },
});

export default FAQScreen;
