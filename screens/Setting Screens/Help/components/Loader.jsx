import { Animated, StyleSheet, View } from "react-native";
import React, { useEffect, useRef } from "react";
import { Colors } from "constants/theme/colors";
import { Easing } from "react-native-reanimated";

const Loader = () => {
    const rotateAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        const startRotation = () => {
            rotateAnim.setValue(0); // Reset animation value
            Animated.loop(
                Animated.timing(rotateAnim, {
                    toValue: 1,
                    duration: 1000, // Adjust speed
                    easing: Easing.linear,
                    useNativeDriver: true,
                })
            ).start();
        };

        startRotation();
    }, [rotateAnim]);

    const rotateInterpolation = rotateAnim.interpolate({
        inputRange: [0, 1],
        outputRange: ["0deg", "360deg"],
    });

    return (
        <Animated.View
            style={[styles.loader, { transform: [{ rotate: rotateInterpolation }] }]}
        />
    );
};

export default Loader;

const styles = StyleSheet.create({
    loader: {
        borderRadius: 100,
        borderTopWidth: 5,
        borderLeftWidth: 5,
        borderColor: Colors.darkGreen,
        width: 40,
        height: 40,
        marginHorizontal: "auto",
        marginVertical: 10,
    },
});
