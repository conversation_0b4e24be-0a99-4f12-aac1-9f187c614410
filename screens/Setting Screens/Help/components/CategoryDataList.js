import { StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import Animated from 'react-native-reanimated';
import CategoryTopicCard from './CategoryTopicCard';
import Icon from 'react-native-vector-icons/Ionicons';
import { Colors } from 'constants/theme/colors';
import apiClient from 'services/axiosInstance';
import { FlatList } from 'react-native';

const CategoryDataList = ({ categoryId, handleBack, currentTopicId, setCurrentTopicId, scrollToTop }) => {
    const apiUrl = process.env.EXPO_PUBLIC_API_URL;

    const [categoryData, setCategoryData] = useState(null);

    const getCategoryData = async () => {
        try {
            const data = await apiClient.get(`${apiUrl}/help/${categoryId}`);
            return data.help;
        } catch (error) {
            console.log(error);
        }
    }

    useEffect(() => {
        scrollToTop();
        (async () => {
            const newCategoryData = await getCategoryData();
            setCategoryData(newCategoryData);
        })();
    }, [categoryId])

    return (
        <View
            style={[
                styles.topicsContainer
            ]}
        >
            {/* <TouchableOpacity
                activeOpacity={1}
                style={styles.backButton}
                onPress={handleBack}
            >
                <Icon name="arrow-back" size={24} color={Colors.primaryGreen} />
                <Text style={styles.backText}>Back to Categories</Text>
            </TouchableOpacity> */}

            {/* {categoryData?.topics.map((topic, index) => <CategoryTopicCard topic={topic} key={index} currentTopicId={currentTopicId} setCurrentTopicId={(ind) => setCurrentTopicId(ind)} index={index} />)} */}

            <FlatList data={categoryData?.topics} keyExtractor={(item, index) => index} scrollEnabled={false} renderItem={({ item, index }) => {
                return (
                    <CategoryTopicCard topic={item} currentTopicId={currentTopicId} setCurrentTopicId={(ind) => setCurrentTopicId(ind)} index={index} />
                )
            }} />
        </View>
    )
}

export default CategoryDataList

const styles = StyleSheet.create({
    topicsContainer: {
        // position: 'absolute',
        // top: -25,
        // left: 0,
        // right: 0,
        // backgroundColor: Colors.white,
        // padding: 20,
    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
    },
    backText: {
        marginLeft: 10,
        fontSize: 16,
        color: Colors.primaryGreen,
        fontFamily: 'Exo_700Bold',
    },
})