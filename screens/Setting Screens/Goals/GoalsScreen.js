import React, { useState, useEffect, useRef } from "react";
import { View, Text, StyleSheet, TouchableWithoutFeedback } from "react-native";
import {
  CustomButton,
  CustomSelect,
  CustomLoader,
} from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import {
  physicalOptions,
  movementOptions,
  mindfulnessOptions,
  sleepOptions,
  deviceUsageLimitOptions,
} from "constants/options";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { screenWidth } from "constants/sizes";
import goalsService from "services/goalsService";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { ThemeFonts } from "constants/theme/fonts";
import { ScrollView } from "react-native-gesture-handler";

export const GoalsScreen = () => {
  const scrollViewRef = useRef();

  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [errors, setErrors] = useState({});
  const [initialGoals, setInitialGoals] = useState({
    physical_goal: "",
    activity_goal: "",
    mind_goal: "",
    sleep_goal: "",
    deviceUsageLimit: 0,
  });

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

  const [goals, setGoals] = useState({
    physical_goal: "",
    activity_goal: "",
    mind_goal: "",
    sleep_goal: "",
    deviceUsageLimit: 0,
  });

  useEffect(() => {
    const fetchUserGoals = async () => {
      try {
        const { data } = await goalsService.getGoals();

        // Convert array format into an object with key-value pairs
        const goalMap = data.goals.reduce((acc, goal) => {
          acc[`${goal.goal_type}_goal`] = goal.selected_goal;
          return acc;
        }, {});
        setGoals({
          physical_goal: goalMap.physical_goal || "",
          activity_goal: goalMap.movement_goal || "",
          mind_goal: goalMap.mindfulness_goal || "",
          sleep_goal: goalMap.sleep_goal || "",
          deviceUsageLimit: (data.deviceUsageLimit == null ? "null" : data.deviceUsageLimit) || 0,
        });
        setInitialGoals({
          physical_goal: goalMap.physical_goal || "",
          activity_goal: goalMap.movement_goal || "",
          mind_goal: goalMap.mindfulness_goal || "",
          sleep_goal: goalMap.sleep_goal || "",
          deviceUsageLimit: data.deviceUsageLimit || 0,
        });
      } catch (error) {
        console.error("Error fetching user goals:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserGoals();
  }, []);

  const handleGoalChange = (field, value) => {
    setGoals((prevGoals) => ({
      ...prevGoals,
      [field]: value,
    }));
    // Clear error when field is updated
    if (errors[field]) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [field]: null,
      }));
    }
  };

  const validateGoals = () => {
    const newErrors = {};
    Object.keys(goals).forEach((key) => {
      if (!goals[key]) {
        newErrors[key] = `Please select a ${key
          .replace(/([A-Z])/g, " $1")
          .toLowerCase()} goal`;
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (
      initialGoals.physical_goal == goals.physical_goal &&
      initialGoals.activity_goal == goals.activity_goal &&
      initialGoals.mind_goal == goals.mind_goal &&
      initialGoals.sleep_goal == goals.sleep_goal &&
      initialGoals.deviceUsageLimit == goals.deviceUsageLimit
    ) {
      setIsEditing(false);
      return;
    }

    // if (!validateGoals()) return;

    setLoading(true);
    try {
      const goalsData = await goalsService.updateGoal(goals);
      // console.log("Goals updated === ", goalsData);
      setInitialGoals(goals);
      setIsEditing(false);
      setErrors({});
    } catch (error) {
      console.error("Error updating user goals:", error);
    } finally {
      setLoading(false);
    }
  };

  const toggleEdit = () => {
    if (isEditing) {
      handleSave();
      scrollToOffset(0);
    } else {
      setIsEditing(true);
    }
    setCurrentOpenDropdown(null);
  };

  const scrollToOffset = (y) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: true });
  };

  if (loading) {
    return (
      <AppLayout illustration={false}>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout illustration={false}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        ref={scrollViewRef}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <TouchableWithoutFeedback
          onPress={() => {
            setCurrentOpenDropdown(null);
          }}
          style={styles.content}
        >
          <View style={{ flex: 1, justifyContent: "space-between" }}>
            <View style={{ flex: 1 }}>
              {!isEditing ? (
                <Text style={styles.menuHeader}>Goals</Text>
              ) : (
                <View style={styles.editHeader}>
                  <Text style={[styles.menuHeader, { marginRight: 5 }]}>
                    Edit Goals
                  </Text>
                  <MaterialIcons name="edit" size={32} color={Colors.black} />
                </View>
              )}
              <View style={styles.form}>
                <View>
                  <Text style={styles.label}>Physical</Text>
                  <View style={{ marginVertical: 10 }}>
                    <CustomSelect
                      options={physicalOptions}
                      selectedValue={goals.physical_goal}
                      onValueChange={(value) =>
                        handleGoalChange("physical_goal", value)
                      }
                      label="Physical Goals"
                      disabled={!isEditing}
                      containerStyle={{ marginBottom: 20 }}
                      triggerZ={10}
                      listZ={9}
                      currentOpenDropdown={currentOpenDropdown}
                      changeBG={true}
                      scrollToOffset={scrollToOffset}
                      dropdownId={1}
                      setCurrentOpenDropdown={(id) =>
                        setCurrentOpenDropdown(id)
                      }
                      clearValidationError={() => {
                        setErrors((prev) => ({ ...prev, physical: "" }));
                      }}
                    />
                  </View>
                  {errors.physical && (
                    <Text style={styles.error}>{errors.physical}</Text>
                  )}
                </View>
                <View>
                  <Text style={styles.label}>Movement/Activity</Text>
                  <View style={{ marginVertical: 10 }}>
                    <CustomSelect
                      options={movementOptions}
                      selectedValue={goals.activity_goal}
                      onValueChange={(value) =>
                        handleGoalChange("activity_goal", value)
                      }
                      label="Activity Goals"
                      disabled={!isEditing}
                      triggerZ={8}
                      listZ={7}
                      currentOpenDropdown={currentOpenDropdown}
                      changeBG={true}
                      scrollToOffset={scrollToOffset}
                      dropdownId={2}
                      setCurrentOpenDropdown={(id) =>
                        setCurrentOpenDropdown(id)
                      }
                    />
                  </View>
                  {errors.movement && (
                    <Text style={styles.error}>{errors.movement}</Text>
                  )}
                </View>
                <Text style={styles.label}>Mindfulness</Text>
                <View style={{ marginVertical: 10 }}>
                  <CustomSelect
                    options={mindfulnessOptions}
                    selectedValue={goals.mind_goal}
                    onValueChange={(value) =>
                      handleGoalChange("mind_goal", value)
                    }
                    label="Mindfulness Goals"
                    disabled={!isEditing}
                    triggerZ={6}
                    listZ={5}
                    currentOpenDropdown={currentOpenDropdown}
                    changeBG={true}
                    scrollToOffset={scrollToOffset}
                    dropdownId={3}
                    setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                  />
                </View>
                {errors.mindfulness && (
                  <Text style={styles.error}>{errors.mindfulness}</Text>
                )}
                <Text style={styles.label}>Sleep</Text>
                <View style={{ marginVertical: 10 }}>
                  <CustomSelect
                    options={sleepOptions}
                    selectedValue={goals.sleep_goal}
                    onValueChange={(value) =>
                      handleGoalChange("sleep_goal", value)
                    }
                    label="Sleep Goals"
                    disabled={!isEditing}
                    triggerZ={4}
                    listZ={3}
                    currentOpenDropdown={currentOpenDropdown}
                    changeBG={true}
                    scrollToOffset={scrollToOffset}
                    dropdownId={4}
                    setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                  />
                </View>
                {errors.sleep && (
                  <Text style={styles.error}>{errors.sleep}</Text>
                )}

                <Text style={styles.label}>Device usage</Text>
                <View style={{ marginVertical: 10 }}>
                  <CustomSelect
                    options={deviceUsageLimitOptions}
                    selectedValue={goals.deviceUsageLimit}
                    onValueChange={(value) =>
                      handleGoalChange("deviceUsageLimit", value)
                    }
                    label="Device Usage"
                    disabled={!isEditing}
                    triggerZ={2}
                    listZ={1}
                    currentOpenDropdown={currentOpenDropdown}
                    changeBG={true}
                    scrollToOffset={scrollToOffset}
                    dropdownId={5}
                    setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                  />
                </View>
                {errors.deviceUsageLimit && (
                  <Text style={styles.error}>{errors.deviceUsageLimit}</Text>
                )}
              </View>
            </View>
            <View
              style={[
                styles.buttonContainer,
                !isEditing
                  ? { flexDirection: "row", justifyContent: "flex-end" }
                  : {},
              ]}
            >
              {isEditing && (
                <CustomButton
                  title="Cancel"
                  onPress={() => {
                    setGoals(initialGoals);
                    setCurrentOpenDropdown(null);
                    setTimeout(() => setIsEditing(!isEditing), 0);
                    scrollToOffset(0);
                    setErrors({});
                  }}
                  style={{
                    width: 120,
                  }}
                />
              )}
              <CustomButton
                title={isEditing ? "Save Goals" : "Edit Goals"}
                onPress={toggleEdit}
                style={{
                  width: 120,
                }}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  content: {
    width: screenWidth,
    left: -20,
    backgroundColor: Colors.white,
    padding: 20,
    zIndex: 100,
  },
  menuHeader: {
    fontSize: 32,
    top: -15,
    color: Colors.black,
    textAlign: "start",
    marginHorizontal: 24,
    marginVertical: 10,
    fontFamily: "Exo_700Bold",
  },
  form: {
    marginTop: 10,
    position: "relative",
  },
  label: {
    color: Colors.darkGray,
    fontSize: 12,
    textTransform: "capitalize",
    fontFamily: ThemeFonts.Exo_500,
    marginHorizontal: 24,
    top: 6,
    fontStyle: "italic",
  },
  error: {
    color: "red",
    fontSize: 12,
    marginBottom: 10,
    marginHorizontal: 24,
  },
  buttonContainer: {
    marginTop: 20,
    justifyContent: "space-between",
    flexDirection: "row",
    marginBottom: 80,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
  editHeader: {
    flexDirection: "row",
  },
});
