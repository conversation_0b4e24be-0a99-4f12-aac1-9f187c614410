import {
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useRef, useState, useEffect } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import { useNavigation, useRoute } from "@react-navigation/native";
import { CustomAlert, CustomButton, CustomLoader } from "components/CustomAction";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import useNutritionMealStore from "store/nutritionMealsStore";
import * as ImagePicker from "expo-image-picker";
import CustomMealImage from "../components/CustomImageWithTextInput";
import SearchBar from "components/CustomAction/SearchBar";
import AddedIngredientCard from "./../components/AddedIngredientCard";
import NutrientCard from "../components/NutrientCard";
import { mealRecordsService } from "services/mealRecordsService";
import useIngredientsStore from "store/ingredientsStore";
import { recipesService } from "services/recipesService";
import getNutrients from "../utils/getNutrients";
import { screenHeight } from "constants/sizes";

const Measurement_Options = ["grams", "milligrams", "oz"];

const Quantity_Options = ["small", "medium", "large"];

const AddCustomUserMeal = () => {
  const navigate = useNavigation();
  const route = useRoute();

  const navigator = useNavigation();
  const scrollViewRef = useRef();
  const meals = useNutritionMealStore((state) => state.meals);
  const selectedMealQuantity = useIngredientsStore((state) => state.selectedMealQuantity);
  const addedIngredient = useIngredientsStore((state) => state.ingredients);

  const addMealData = useNutritionMealStore((state) => state.addMealData);
  const setSelectedMealQuantity = useIngredientsStore((state) => state.setSelectedMealQuantity);

  const [error, setError] = useState();

  // Custom meal data
  const [mealName, setMealName] = useState("");
  const [mealImage, setMealImage] = useState(null);
  const [selectedMeasurement, setSelectedMeasurement] = useState("grams");

  const [ingredientQuery, setIngredientQuery] = useState("");
  const [loadingIngredients, setLoadingIngredients] = useState(false);
  const [ingredients, setIngredients] = useState([]);
  const [page, setPage] = useState(1);
  const [hasMoreIngredients, setHasMoreIngredients] = useState(true);

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [isAddingMeal, setIsAddingMeal] = useState(false);
  const [currentScrollPos, setCurrentScrollPos] = useState(0);
  const [addedIngredientBoxHeight, setAddedIngredientBoxHeight] = useState(0);

  const pickImage = async () => {
    // Request permission
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== "granted") {
      setError("Permission to access media library is required!");
      return;
    }

    // Launch image picker
    let result = await ImagePicker.launchImageLibraryAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      setMealImage(result.assets[0].uri);
    }
  };

  const scrollToOffset = (y, animated = true) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: animated, duration: 300 });
  };

  const getIngredientsData = async () => {
    if (loadingIngredients || !hasMoreIngredients) return;
    setLoadingIngredients(true);

    const response = await mealRecordsService.getIngredients(ingredientQuery, page);

    if (response.success) {
      if (response.data.length > 0) {
        setIngredients((prev) => [...prev, ...response.data]);
        setPage((prevPage) => prevPage + 1);
        setHasMoreIngredients(true);
      } else {
        setHasMoreIngredients(false);
      }
    } else {
      setError(response.error);
      setHasMoreIngredients(false);
    }

    setLoadingIngredients(false);
  };

  const handleLoadMoreIngredients = () => {
    if (!loadingIngredients && hasMoreIngredients) {
      getIngredientsData();
    }
  };

  useEffect(() => {
    let isActive = true;
    setLoadingIngredients(true);
    setPage(1);
    setHasMoreIngredients(true);

    (async () => {
      setIngredients([]);
      const response = await mealRecordsService.getIngredients(ingredientQuery, 1);

      if (isActive && response.success) {
        setIngredients(response.data);
        setPage(2);
        setHasMoreIngredients(response.data.length > 0);
      } else {
        setIngredients([]);
        setPage(1);
        setHasMoreIngredients(false);
        setError(response.error);
      }
      setLoadingIngredients(false);
    })();

    return () => {
      isActive = false;
    };
  }, [ingredientQuery]);

  const handleSave = async () => {
    const form = new FormData();

    form.append("title", mealName);
    form.append("ingredients", addedIngredient.map((ingredient) => ingredient.recipeId));
    form.append("thumbnailFile", {
      uri: mealImage,
      name: "image.jpg",
      type: "image/jpg",
    });
    form.append("nutritionByQuantity", JSON.stringify([
      {
        quantity: "small",
        protein: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.protein, 0), "small"),
        calories: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.calories, 0), "small"),
        fats: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.fats, 0), "small"),
        fiber: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.fiber, 0), "small"),
        carbs: addedIngredient.reduce((total, ingredient) => total + ingredient.carbs, 0),
      },
      {
        quantity: "medium",
        protein: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.protein, 0), "medium"),
        calories: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.calories, 0), "medium"),
        fats: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.fats, 0), "medium"),
        fiber: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.fiber, 0), "medium"),
        carbs: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.carbs, 0), "medium"),
      },
      {
        quantity: "large",
        protein: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.protein, 0), "large"),
        calories: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.calories, 0), "large"),
        fats: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.fats, 0), "large"),
        fiber: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.fiber, 0), "large"),
        carbs: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.carbs, 0), "large"),
      },
    ]));

    setIsAddingMeal(true);
    const res = await recipesService.createUserRecipe({ data: form });

    if (res.success) {
      addMealData({
        recipeId: res.data.id,
        measurement: selectedMeasurement,
        quantity: selectedMealQuantity,
        name: mealName,
        image: res.data.thumbnailUrl,
        calories: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.calories, 0), selectedMealQuantity),
        protein: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.protein, 0), selectedMealQuantity),
        fiber: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.fiber, 0), selectedMealQuantity),
        fats: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.fats, 0), selectedMealQuantity),
        carbs: getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.carbs, 0), selectedMealQuantity),
      });
      navigator.goBack();
    }
    else {
      setError(res.error);
      setIsAddingMeal(false);
    }
  };

  if (isAddingMeal) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  console.log("Scroll view post :", currentScrollPos);

  return (
    <AppLayout>
      <ScrollView
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        ref={scrollViewRef}
        contentContainerStyle={{
          flexGrow: 1,
        }}
        onScroll={(e) => {
          setCurrentScrollPos(e.nativeEvent.contentOffset.y);
        }}
      >
        <TouchableWithoutFeedback
          onPress={() => {
            setCurrentOpenDropdown(null);
          }}
        >
          <View style={{
            flex: 1, flexDirection: "column", marginHorizontal: 8,
            paddingBottom: screenHeight * 0.45,
          }}>
            <View style={[styles.header, { marginBottom: 16 }]}>
              <Text style={styles.headerTitle}>
                {route.params?.meal_name || "Add Custom Meal"}
              </Text>
              <Text style={styles.totalCaloriesText}>{`${meals.reduce((total, meal) => total + meal.calories, 0) + getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.calories, 0), selectedMealQuantity)
                } kcal`}</Text>
            </View>

            <CustomMealImage mealName={mealName} setMealName={setMealName} editable={true} mealImage={mealImage} pickImage={pickImage} calories={getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.calories, 0), selectedMealQuantity)} />

            <View style={{ gap: 24 }}>
              {
                addedIngredient.length != 0 && (
                  <>
                    <FlatList
                      data={addedIngredient}
                      keyExtractor={(_, index) => index.toString()}
                      scrollEnabled={false}
                      contentContainerStyle={{ gap: 16 }}
                      style={{ marginTop: 24 }}
                      renderItem={({ item, index }) => (
                        <AddedIngredientCard
                          id={index}
                          ingredient={item}
                          route_params={route.params}
                        />
                      )}
                      onLayout={(event) => {
                        setAddedIngredientBoxHeight(event.nativeEvent.layout.height);
                      }}
                    />

                    <CustomSelectWithLabel
                      separateLabel="What is the measure?"
                      label="Add measure"
                      selectedValue={selectedMeasurement}
                      onValueChange={(value) => setSelectedMeasurement(value)}
                      options={
                        Measurement_Options.map((value) => {
                          return {
                            label: value,
                            value: value
                          };
                        })
                      }
                      dropdownId={"measurement_options"}
                      currentOpenDropdown={currentOpenDropdown}
                      setCurrentOpenDropdown={(value) => {
                        setCurrentOpenDropdown(value);
                      }}
                      scrollToOffset={(value) => scrollToOffset(value + addedIngredientBoxHeight - 200)}
                      triggerZ={10}
                      listZ={9}
                      duration={0}
                    />

                    <CustomSelectWithLabel
                      separateLabel="What is the quantity?"
                      label="Add quantity"
                      selectedValue={selectedMealQuantity}
                      onValueChange={(value) => setSelectedMealQuantity(value)}
                      options={
                        Quantity_Options.map((value) => {
                          return {
                            label: value,
                            value: value
                          };
                        })
                      }
                      dropdownId={"quantity_options"}
                      currentOpenDropdown={currentOpenDropdown}
                      setCurrentOpenDropdown={(value) => {
                        setCurrentOpenDropdown(value);
                      }}
                      scrollToOffset={(value) => scrollToOffset(value + addedIngredientBoxHeight - 200)}
                      triggerZ={8}
                      listZ={7}
                      duration={0}
                    />

                    <View style={{ gap: 35, marginHorizontal: 8, marginTop: 24 }}>
                      <View style={styles.nutritionCardContainer}>
                        <NutrientCard label="Protein" value={getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.protein, 0), selectedMealQuantity)} measurement={selectedMeasurement} />
                        <NutrientCard label="Fats" value={getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.fats, 0), selectedMealQuantity)} measurement={selectedMeasurement} />
                      </View>
                      <View style={styles.nutritionCardContainer}>
                        <NutrientCard label="Carbs" value={getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.carbs, 0), selectedMealQuantity)} measurement={selectedMeasurement} />
                        <NutrientCard label="Fiber" value={getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.fiber, 0), selectedMealQuantity)} measurement={selectedMeasurement} />
                      </View>
                    </View>
                  </>
                )
              }

              <View style={{ marginTop: 16, gap: 16 }}>
                {addedIngredient?.length != 0 && (
                  <View style={[styles.header]}>
                    <Text style={styles.headerTitle}>Add More </Text>
                  </View>
                )}
                <SearchBar
                  query={ingredientQuery}
                  keyExtractor={({ item }) => item.id + ""}
                  options={ingredients.map((ingredient) => ({
                    label: ingredient.name,
                    value: ingredient.id,
                  }))}
                  onValueChange={(value) => {
                    setIngredients([]);
                    setIngredientQuery(value);
                  }}
                  onPress={(id) => {
                    navigate.navigate("Add Meal Ingredient", {
                      id: id,
                      meal_name: route.params.meal_name,
                    });
                    setCurrentOpenDropdown(null);
                  }}
                  id={"ingredient_search_bar"}
                  currentOpenId={currentOpenDropdown}
                  setCurrentOpenId={setCurrentOpenDropdown}
                  onEndReached={handleLoadMoreIngredients}
                  loading={loadingIngredients}
                  scrollToOffset={(value) => scrollToOffset(value + currentScrollPos, false)}
                  triggerZ={6}
                  listZ={5}
                  triggerBgColor={Colors.lightGray}
                  triggerTextColor={Colors.black}
                  placeholder="Search for ingredients"
                  remeasureYpos={addedIngredient?.length}
                />
              </View>

              <View
                style={{
                  marginTop: 8,
                  alignItems: "flex-end",
                  marginHorizontal: 8,
                }}
              >
                <CustomButton
                  title={"Add meal"}
                  onPress={handleSave}
                  disabled={
                    !(
                      mealName &&
                      mealImage && selectedMeasurement && selectedMealQuantity && addedIngredient.length != 0
                    ) || isAddingMeal
                  }
                  style={{
                    width: "auto",
                    paddingHorizontal: 32
                  }}
                />
              </View>
            </View>
            <CustomAlert
              visible={!!error}
              title="Error"
              message={error}
              buttons={[
                {
                  text: "OK",
                  onPress: () => { },
                  style: "cancelButton",
                },
              ]}
              onClose={() => setError(null)}
            />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default AddCustomUserMeal;

const styles = StyleSheet.create({
  header: {
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  totalCaloriesText: {
    fontSize: 25,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_600,
  },
  nutritionCardContainer: {
    flexDirection: "row",
    gap: 24
  },
  loaderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    bottom: 50,
  }
});
