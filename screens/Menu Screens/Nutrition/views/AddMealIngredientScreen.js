import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useEffect, useRef, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import { useNavigation, useRoute } from "@react-navigation/native";
import { CustomButton, CustomLoader } from "components/CustomAction";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import useNutritionMealStore from "store/nutritionMealsStore";
import * as ImagePicker from "expo-image-picker";
import CustomMealImage from "../components/CustomImageWithTextInput"
import SearchBar from "components/CustomAction/SearchBar";
import AddedIngredientCard from "../components/AddedIngredientCard"
import NutrientCard from "../components/NutrientCard";
import { mealRecordsService } from "services/mealRecordsService";
import useIngredientsStore from "store/ingredientsStore";
import getNutrients from "../utils/getNutrients";

const Measurement_Options = ["grams", "milligrams", "oz"];

const AddMealIngredientScreen = () => {
  const navigate = useNavigation();
  const route = useRoute();

  const navigator = useNavigation();
  const scrollViewRef = useRef();
  const meals = useNutritionMealStore((state) => state.meals);

  const selectedMealQuantity = useIngredientsStore((state) => state.selectedMealQuantity);
  const addedIngredient = useIngredientsStore((state) => state.ingredients);

  const addIngredient = useIngredientsStore((state) => state.addIngredient);

  const [error, setError] = useState();

  // Custom meal data
  const [mealName, setMealName] = useState(null);
  const [mealImage, setMealImage] = useState(null);
  const [ingredientMeasurements, setIngredientMeasurements] = useState([]);

  const [selectedMeasurement, setSelectedMeasurement] = useState(null);
  const [selectedQuantity, setSelectedQuantity] = useState(null);

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    (async () => {
      const res = await mealRecordsService.getIngredient(route.params?.id);
      if (res.success) {
        setMealName(res.data.name);
        setMealImage(res.data.thumbnailUrl);
        setIngredientMeasurements(res.data.ingredientNutritionByQuantity);
      }
      else {
        setError(res.error);
      }
      setLoading(false);
    })();
  }, [])

  const scrollToOffset = (y, animated = true) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: animated, duration: 300 });
  };

  const handleAddIngredient = () => {
    addIngredient({
      recipeId: route.params?.id,
      measurement: selectedMeasurement,
      quantity: selectedQuantity,
      name: mealName,
      image: mealImage,
      calories: ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.calories,
      protein: ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.protein,
      fiber: ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.fiber,
      fats: ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.fats,
      carbs: ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.carbs,
    });
    navigator.goBack();
  }

  return (
    <AppLayout>
      <ScrollView
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        ref={scrollViewRef}
        contentContainerStyle={{
          flexGrow: 1,
        }}
      >
        <TouchableWithoutFeedback
          onPress={() => {
            setCurrentOpenDropdown(null);
          }}
        >
          <View style={{
            flex: 1, flexDirection: "column", gap: 32, marginHorizontal: 8,
            paddingBottom: 400,
          }}>
            <View style={styles.header}>
              <Text style={styles.headerTitle}>
                {route.params?.meal_name || "Add Custom Meal"}
              </Text>
              <Text style={styles.totalCaloriesText}>{`${meals.reduce((total, meal) => total + meal.calories, 0) + (
                selectedQuantity ? getNutrients(ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.calories, selectedMealQuantity) : 0
              ) + getNutrients(addedIngredient.reduce((total, ingredient) => total + ingredient.calories, 0), selectedMealQuantity)
                } kcal`}</Text>
            </View>

            <CustomMealImage mealName={mealName} setMealName={setMealName} mealImage={mealImage} pickImage={() => { }} editable={false} isLoading={loading} calories={ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.calories} />

            <CustomSelectWithLabel
              separateLabel="What is the measure?"
              label="Add measure"
              selectedValue={selectedMeasurement}
              onValueChange={(value) => setSelectedMeasurement(value)}
              options={
                Measurement_Options.map((value) => {
                  return {
                    label: value,
                    value: value
                  };
                })
              }
              dropdownId={"measurement_options"}
              currentOpenDropdown={currentOpenDropdown}
              setCurrentOpenDropdown={(value) => {
                setCurrentOpenDropdown(value);
              }}
              scrollToOffset={(value) => scrollToOffset(value - 300)}
              triggerZ={10}
              listZ={9}
              isLoading={loading}
            />

            <CustomSelectWithLabel
              separateLabel="What is the quantity?"
              label="Add quantity"
              selectedValue={selectedQuantity}
              onValueChange={(value) => setSelectedQuantity(value)}
              options={
                ingredientMeasurements.map((measure) => {
                  return {
                    label: String(measure.quantity).split("_").join(" "),
                    value: measure.quantity
                  };
                })
              }
              dropdownId={"quantity_options"}
              currentOpenDropdown={currentOpenDropdown}
              setCurrentOpenDropdown={(value) => {
                setCurrentOpenDropdown(value);
              }}
              scrollToOffset={(value) => scrollToOffset(value - 300)}
              triggerZ={8}
              listZ={7}
              isLoading={loading}
            />

            {
              (!loading && selectedMeasurement && selectedQuantity) && (
                <View style={{ gap: 35, marginHorizontal: 8, marginTop: 24 }}>
                  <View style={styles.nutritionCardContainer}>
                    <NutrientCard label="Protein" value={ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.protein} measurement={selectedMeasurement} />
                    <NutrientCard label="Fats" value={ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.fats} measurement={selectedMeasurement} />
                  </View>
                  <View style={styles.nutritionCardContainer}>
                    <NutrientCard label="Carbs" value={ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.carbs} measurement={selectedMeasurement} />
                    <NutrientCard label="Fiber" value={ingredientMeasurements.filter(item => item.quantity == selectedQuantity)[0]?.fiber} measurement={selectedMeasurement} />
                  </View>
                </View>
              )
            }

            <View
              style={{
                marginTop: 8,
                alignItems: "flex-end",
                marginHorizontal: 8,
              }}
            >
              <CustomButton
                title={"Add"}
                onPress={handleAddIngredient}
                style={{
                  width: "auto",
                  paddingHorizontal: 32
                }}
                disabled={
                  !(
                    mealName &&
                    mealImage && selectedMeasurement && selectedQuantity
                  )
                }
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default AddMealIngredientScreen;

const styles = StyleSheet.create({
  header: {
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  totalCaloriesText: {
    fontSize: 25,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_600,
  },
  nutritionCardContainer: {
    flexDirection: "row",
    gap: 24
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});
