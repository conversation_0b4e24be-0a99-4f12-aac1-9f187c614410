import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { CustomButton } from 'components/CustomAction';
import { Colors } from 'constants/theme/colors';
import { useNavigation } from '@react-navigation/native';
import { ThemeFonts } from 'constants/theme/fonts';
import useNutritionMealStore from 'store/nutritionMealsStore';
import { getMealTime } from '../utils/getMealTime';

const NutritionOverviewCard = ({ record, selectedDate, disabled = false }) => {
    const navigation = useNavigation();
    const resetMealData = useNutritionMealStore(state => state.resetMeals);

    return (
        <View key={record?.id} style={[
            styles.reminderCard,
            { backgroundColor: record.meals.length > 0 ? Colors.primaryGreen : "#7AA9A0" },
            { opacity: !disabled ? 1 : 0.7 }
        ]}>
            <View style={styles.reminderContent}>
                <View style={styles.reminderHeader}>
                    <Text style={[styles.mealTimeRange, record.meals.length > 0 && { color: Colors.white }]}>
                        {getMealTime(record.mealName)}
                    </Text>
                    <Text style={[styles.reminderTitle, record.meals.length > 0 && { color: Colors.white }]} numberOfLines={1} ellipsizeMode='tail'>
                        {record.mealName}
                    </Text>

                    {record.meals.length > 0 ? (
                        <Text style={[styles.reminderDesc, { color: Colors.lightGreen }]} numberOfLines={1}>
                            {
                                record.meals.map(meal => {
                                    const mealObj = typeof meal === 'object' ? meal : {};
                                    const title = mealObj.title || mealObj.quantity || mealObj.name || mealObj.recipe?.title || 'Meal';
                                    return `${title}`;
                                }).join(" & ")
                            }
                        </Text>
                    ) : (
                        <Text style={[styles.reminderDesc, { fontFamily: ThemeFonts.Exo_900, color: Colors.white }]}>
                            _____
                        </Text>
                    )}
                </View>
                <View style={{ justifyContent: "center", gap: 5 }}>
                    <CustomButton
                        title={record.meals.length > 0 ? "Edit" : "Add"}
                        disabled={disabled}
                        onPress={() => {
                            resetMealData();
                            if (record?.id) {
                                navigation.navigate("Edit Nutrition", { mealRecordId: record.id, mealType: record.mealName, selectedDate: selectedDate });
                            } else {
                                navigation.navigate("Edit Nutrition", { mealType: record.mealName, selectedDate: selectedDate });
                            }
                        }}
                        style={styles.editButton}
                        disabledBgColor={Colors.primaryPurple}
                        disabledTextColor={Colors.white}
                    />
                    <Text style={[styles.totalCalText, record.meals.length > 0 && { color: Colors.lightGreen }]}>
                        {record.meals.length > 0 ?
                            (() => {
                                const totalCalories = record.meals.length > 0 ?
                                    record.meals.reduce((sum, meal) => {
                                        // Handle the case where meal might be an Object object in the API response
                                        const mealObj = typeof meal === 'object' ? meal : {};

                                        // Log the meal object to see what properties it has
                                        // console.log('Meal object for calories:', JSON.stringify(mealObj, null, 2));

                                        // Try to get calories from different possible properties
                                        let calories = 0;

                                        if (mealObj.calories) {
                                            calories = mealObj.calories;
                                        } else if (mealObj.nutritionByQuantity && mealObj.nutritionByQuantity.calories) {
                                            calories = mealObj.nutritionByQuantity.calories;
                                        } else if (mealObj.recipe && mealObj.recipe.calories) {
                                            calories = mealObj.recipe.calories;
                                        } else if (mealObj.nutritionByQuantity && Array.isArray(mealObj.nutritionByQuantity)) {
                                            // If nutritionByQuantity is an array, find the selected quantity
                                            const selectedQuantity = mealObj.nutritionByQuantity.find(q => q.quantity === mealObj.selectedQuantity);
                                            if (selectedQuantity && selectedQuantity.calories) {
                                                calories = selectedQuantity.calories;
                                            }
                                        }
                                        return sum + (parseInt(calories) || 0);
                                    }, 0) : 0;

                                return `${totalCalories} cal`;
                            })() :
                            "0 cal"
                        }
                    </Text>
                </View>
            </View>
        </View>
    );
};

export default NutritionOverviewCard;

const styles = StyleSheet.create({
    reminderCard: {
        backgroundColor: Colors.primaryGreen,
        borderRadius: 24,
        padding: 24,
        paddingBottom: 32,
        // marginVertical: 16,
        // marginBottom: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    reminderContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
    },
    reminderHeader: {
        flex: 1,
    },
    reminderAlarmTime: {
        fontSize: 14,
        color: Colors.white,
        fontFamily: ThemeFonts.Exo_400,
        marginBottom: 2,
    },
    reminderTitle: {
        fontSize: 30,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.white,
        marginTop: 8
    },
    reminderDesc: {
        fontSize: 12,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.lightGreen,
        marginTop: 2,
        marginRight: 8
    },
    mealTimeRange: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.white,
        marginTop: 2,
        marginBottom: 4,
        opacity: 0.8
    },
    editButton: {
        // marginLeft: 10,
        maxWidth: 72
    },
    totalCalText: {
        fontSize: 10,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.lightGreen,
        textAlign: "center",
    }
});
