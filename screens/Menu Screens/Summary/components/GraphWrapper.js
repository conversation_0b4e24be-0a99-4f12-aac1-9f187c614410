import { StyleSheet, Text, View } from "react-native";
import React, { memo } from "react";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const GraphWrapper = ({
  Graph,
  graphName,
  userCurrValue,
  userGoal,
  isLoadingMetaData = false,
  currentOpenDropdown,
  setCurrentOpenDropdown,
}) => {
  return (
    <View>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{graphName}</Text>
        <SkeletonItem width={"25%"} height={18} borderRadius={4} isLoading={isLoadingMetaData}>
          <Text style={styles.goalsText}>{`${userCurrValue}${userGoal ? "/" + userGoal : ""}`}</Text>
        </SkeletonItem>
      </View>
      {
        !Graph ? (
          <View style={styles.skeletonGraphContainer}>
            <SkeletonItem height={28} width={"30%"} borderRadius={25} />
            <SkeletonItem height={32} width={"50%"} borderRadius={10} />

            <SkeletonItem height={180} borderRadius={25} colors={[
              'rgba(255, 159, 193, 0.01)',
              'rgba(255, 159, 193, 0.15)',
              'rgba(255, 159, 193, 0.55)',
              'rgba(255, 159, 193, 0.15)',
              'rgba(255, 159, 193, 0.01)',
            ]} />
          </View>
        ) : (
          <Graph currentOpenDropdown={currentOpenDropdown} setCurrentOpenDropdown={setCurrentOpenDropdown} />
        )
      }
    </View>
  );
};

export default GraphWrapper;

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginHorizontal: 22,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    color: Colors.black,
    fontFamily: ThemeFonts.Exo_700,
  },
  goalsText: {
    fontSize: 16,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
  skeletonGraphContainer: {
    backgroundColor: Colors.lightPurple,
    borderRadius: 25,
    padding: 16,
    paddingHorizontal: 24,
    gap: 16,
  }
});
