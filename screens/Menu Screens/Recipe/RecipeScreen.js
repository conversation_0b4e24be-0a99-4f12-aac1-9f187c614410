import {
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useEffect, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import {
  CustomAlert,
  CustomLoader,
  CustomSelect,
} from "components/CustomAction";
import { recipesService } from "services/recipesService";
import RecipeCard from "components/CustomCards/RecipeCard";
import FlatListBottomLoader from "components/Loaders/FlatListBottomLoader";

const RECIPES_CATEGORIES = [
  {
    label: "All",
    value: "All",
  },
  {
    label: "Breakfast",
    value: "breakfast",
  },
  {
    label: "Lunch",
    value: "lunch",
  },
  {
    label: "Dinner",
    value: "dinner",
  },
  {
    label: "Snack",
    value: "snacks",
  },
  {
    label: "My recipes",
    value: "My recipes",
  },
];

const RecipeScreen = () => {
  const [selectedCategory, setSelectedCategory] = useState();

  const [isLoadingRecipes, setIsLoadingRecipes] = useState(true);
  const [recipes, setRecipes] = useState([]);
  const [page, setPage] = useState(1);
  const [hasMoreRecipes, setHasMoreRecipes] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const [error, setError] = useState(null);

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

  const getRecipes = async () => {
    if (isLoadingRecipes || !hasMoreRecipes) return;
    setIsLoadingRecipes(true);

    const response = await recipesService.getAllRecipes({
      mealType: selectedCategory,
      page: page,
    });

    if (response.success) {
      setRecipes((prevData) => [...prevData, ...response.data]);
      setPage((prevPage) => prevPage + 1);
      setHasMoreRecipes(response.data.length > 0);
    } else {
      setHasMoreRecipes(false);
      setError(response.error);
    }

    setIsLoadingRecipes(false);
  };

  const onRefresh = async () => {
    setPage(1);
    setIsLoadingRecipes(true);
    setHasMoreRecipes(true);
    setIsRefreshing(true);
    setRecipes([]);

    const response = await recipesService.getAllRecipes({
      mealType: selectedCategory,
      page: 1,
    });

    if (response.success) {
      setRecipes(response.data);
      setPage(2);
      setHasMoreRecipes(response.data.length > 0);
    } else {
      setHasMoreRecipes(false);
      setError(response.error);
    }

    setIsLoadingRecipes(false);
    setIsRefreshing(false);
  };

  useEffect(() => {
    let isMounted = true;
    setIsLoadingRecipes(true);
    setPage(1);
    setHasMoreRecipes(true);
    setRecipes([]);

    (async () => {
      const getRecipesRes = await recipesService.getAllRecipes({
        mealType: selectedCategory,
      });

      if (isMounted) {
        if (getRecipesRes.success) {
          setRecipes(getRecipesRes.data);
          setPage(2);
          setHasMoreRecipes(getRecipesRes.data.length > 0);
        } else {
          setError(getRecipesRes.error);
        }
      }

      setIsLoadingRecipes(false);
    })();

    return () => {
      isMounted = false;
    };
  }, [selectedCategory]);

  return (
    <AppLayout>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          { flexGrow: 1 },
          {
            paddingBottom: isLoadingRecipes && hasMoreRecipes ? 90 : 90,
          },
        ]}
        onScroll={(e) => {
          let paddingToBottom = 90;
          paddingToBottom += e.nativeEvent.layoutMeasurement.height;
          if (
            e.nativeEvent.contentOffset.y >=
            e.nativeEvent.contentSize.height - paddingToBottom
          ) {
            getRecipes();
          }
        }}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[Colors.primaryGreen]}
            progressViewOffset={24}
          />
        }
      >
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View style={{ flex: 1 }}>
            <Text style={styles.headerTitle}>Recipes</Text>
            <CustomSelect
              options={RECIPES_CATEGORIES}
              label="Select"
              selectedValue={selectedCategory}
              onValueChange={(value) => setSelectedCategory(value)}
              backgroundColor={Colors.primaryPurple}
              textColor={Colors.white}
              triggerZ={20}
              listZ={19}
              currentOpenDropdown={currentOpenDropdown}
              dropdownId={1}
              setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
              changeBG={true}
            />
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                marginTop: 56,
              }}
            >
              <FlatList
                data={recipes}
                numColumns={2}
                scrollEnabled={false}
                keyExtractor={(item) => item.id}
                contentContainerStyle={{
                  gap: 28,
                }}
                showsVerticalScrollIndicator={false}
                style={{}}
                renderItem={({ item, index }) => (
                  <View style={[index % 2 === 0 ? {} : { marginLeft: 16 }]}>
                    <RecipeCard
                      recipe={item}
                      onPress={() => setCurrentOpenDropdown(null)}
                    />
                  </View>
                )}
                ListFooterComponent={
                  isLoadingRecipes && hasMoreRecipes ? (
                    <FlatListBottomLoader />
                  ) : null
                }
              />
            </View>
            <CustomAlert
              title="Error"
              message={error}
              visible={!!error}
              buttons={[
                { text: "OK", onPress: () => { }, style: "allowButton" },
              ]}
              onClose={() => {
                setError(null);
              }}
            />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default RecipeScreen;

const styles = StyleSheet.create({
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
    paddingLeft: 22,
    marginBottom: 28,
  },
});
