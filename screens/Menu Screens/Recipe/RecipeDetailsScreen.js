import {
  FlatList,
  Image,
  ImageBackground,
  Linking,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { useEffect, useState } from "react";
import React from "react";
import { useRoute } from "@react-navigation/native";
import { recipesService } from "services/recipesService";
import { CustomAlert, CustomLoader } from "components/CustomAction";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import { Feather } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";
import { useAuth } from "context/AuthContext";

const RecipeDetailsScreen = () => {

  const { state: { user } } = useAuth();
  const route = useRoute();

  const [isLoadingRecipes, setIsLoadingRecipes] = useState(true);

  const [recipeDetails, setRecipeDetails] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    (async () => {
      setIsLoadingRecipes(true);
      const response = await recipesService.getSingleRecipe({
        id: route.params.id,
      });

      if (response.success) {
        setRecipeDetails(response.data);
      } else {
        setError(response.error);
      }
      setIsLoadingRecipes(false);
    })();
  }, [route.params.id]);

  return (
    <AppLayout illustration={false} paddingHorizontal={0} paddingTop={0}>
      <ScrollView
        contentContainerStyle={{ paddingBottom: 90 }}
        showsVerticalScrollIndicator={false}
      >
        <TouchableWithoutFeedback>
          <View style={{ flex: 1 }}>
            <View style={{ marginBottom: 32 }}>
              <Image
                source={{ uri: recipeDetails?.thumbnailUrl }}
                style={styles.backgroundImg}
                resizeMode="cover"
              />
              <LinearGradient colors={isLoadingRecipes ? ["rgba(144, 238, 144, 1)", "rgba(255, 255, 255, 0.9)"] : ["rgba(255,255,255,0.5)", "rgba(255, 255, 255, 0.9)"]} style={styles.backgroundImg} />
              < View
                style={{
                  paddingHorizontal: 16,
                  paddingTop: 30,
                  // backgroundColor: "rgba(255,255,255,0.7)",
                }}
              >
                <View style={styles.header}>
                  <SkeletonItem width={"30%"} height={14} borderRadius={10} isLoading={isLoadingRecipes}>
                    <Text style={styles.recommendedText}>{recipeDetails?.author === user.id ? "Custom" : "Recommended"}</Text>
                  </SkeletonItem>
                  <SkeletonItem width={"40%"} height={32} borderRadius={8} isLoading={isLoadingRecipes} style={{ marginVertical: 8 }}>
                    <Text style={styles.headerTitle}>{recipeDetails?.title}</Text>
                  </SkeletonItem>
                  <SkeletonItem width={"20%"} height={24} borderRadius={8} isLoading={isLoadingRecipes}>
                    <View style={styles.flexRow}>
                      <Text style={styles.totalCalories}>
                        {recipeDetails?.nutritionByQuantity[0].calories || 0}
                      </Text>
                      <Text style={styles.kcalText}>kcal</Text>
                    </View>
                  </SkeletonItem>
                </View>
                {
                  recipeDetails?.timeToPrep ? (
                    <View style={styles.servingDetailsSection}>
                      <View>
                        <Text style={styles.servingDetailLabel}>Time to prep</Text>
                        <View style={styles.flexRow}>
                          <Text
                            style={[
                              styles.servingDetailValue,
                              { fontFamily: ThemeFonts.Lexend_500 },
                            ]}
                          >
                            {recipeDetails.timeToPrep}
                          </Text>
                          <Text style={styles.servingDetailValue}>
                            {` minutes`}
                          </Text>
                        </View>
                      </View>
                      <View>
                        <Text
                          style={[
                            styles.servingDetailLabel,
                            { textAlign: "right" },
                          ]}
                        >
                          {String(recipeDetails.nutritionByQuantity[0].quantity)
                            .substring(0, 1)
                            .toUpperCase() +
                            String(
                              recipeDetails.nutritionByQuantity[0].quantity
                            ).substring(1)}{" "}
                          bowl
                        </Text>

                        <View style={styles.flexRow}>
                          <Text
                            style={[
                              styles.servingDetailValue,
                              { fontFamily: ThemeFonts.Lexend_500 },
                            ]}
                          >
                            {recipeDetails.numOfServings || 1}
                          </Text>
                          <Text style={styles.servingDetailValue}>
                            {` serving`}
                          </Text>
                        </View>
                      </View>
                    </View>
                  ) : (
                    <View style={{ height: 80 }}>

                    </View>
                  )
                }
              </View>
            </View>
            <View style={styles.ingredientsSection}>
              <View style={styles.ingredientsHeader}>
                <Text style={styles.ingredientsHeaderText}>Ingredients</Text>
                <TouchableOpacity
                  activeOpacity={0.5}
                  onPress={() => {
                    Linking.openURL(
                      "https://www.google.com/search?q=" +
                      recipeDetails.ingredients.join("+")
                    );
                  }}
                  style={{ flexDirection: "row", alignItems: "center" }}
                  disabled={isLoadingRecipes}
                >
                  <Text
                    style={[
                      styles.lastLogTimeText,
                      {
                        fontSize: 16,
                        fontFamily: ThemeFonts.Exo_500,
                        color: Colors.primaryPurple,
                        marginRight: 5,
                      },
                    ]}
                  >
                    Links
                  </Text>
                  <Feather
                    name={"external-link"}
                    size={16}
                    color={Colors.primaryPurple}
                  />
                </TouchableOpacity>
              </View>
              {
                isLoadingRecipes ? (
                  <View>
                    <SkeletonItem height={20} width={"60%"} borderRadius={8} isLoading={true} style={{ marginVertical: 8 }} />
                    <SkeletonItem height={20} width={"50%"} borderRadius={8} isLoading={true} style={{ marginVertical: 8 }} />
                    <SkeletonItem height={20} width={"70%"} borderRadius={8} isLoading={true} style={{ marginVertical: 8 }} />
                  </View>
                ) : (
                  <FlatList
                    data={recipeDetails?.ingredients}
                    scrollEnabled={false}
                    keyExtractor={(item, index) => index.toString()}
                    renderItem={({ item }) => (
                      <Text style={styles.ingredientText}>- {item}</Text>
                    )}
                  />)
              }
            </View>
            {
              (user.id != recipeDetails?.author && !isLoadingRecipes) && (
                <View style={styles.ingredientsSection}>
                  <Text style={styles.ingredientsHeaderText}>Directions</Text>
                  <Text> {recipeDetails?.directions}</Text>
                </View>
              )
            }
            <CustomAlert
              title="Error"
              message={error}
              visible={!!error}
              buttons={[{ text: "OK", onPress: () => setError(null) }]}
              onClose={() => setError(null)}
            />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout >
  );
};

export default RecipeDetailsScreen;

const styles = StyleSheet.create({
  backgroundImg: {
    width: "100%",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 25,
  },
  header: {
    marginHorizontal: 32,
    marginBottom: 8,
  },
  recommendedText: {
    alignSelf: "flex-start",
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_400,
    backgroundColor: Colors.primaryGreen,
    color: Colors.white,
    paddingHorizontal: 12,
    borderRadius: 10,
  },
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  flexRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    gap: 1,
  },
  totalCalories: {
    fontSize: 25,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Lexend_500,
  },
  kcalText: {
    fontSize: 25,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_600,
  },
  servingDetailsSection: {
    marginHorizontal: 8,
    backgroundColor: Colors.primaryPurple,
    paddingHorizontal: 24,
    borderRadius: 25,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 8,
    top: 30,
  },
  servingDetailLabel: {
    fontSize: 12,
    color: Colors.white,
    fontFamily: ThemeFonts.Exo_500,
  },
  servingDetailValue: {
    fontSize: 19,
    color: Colors.white,
    fontFamily: ThemeFonts.Exo_700,
  },
  ingredientsSection: {
    marginTop: 16,
    marginHorizontal: 16,
    paddingHorizontal: 24,
  },
  ingredientsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  ingredientsHeaderText: {
    fontSize: 29,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  ingredientText: {
    fontSize: 16,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    bottom: 50,
  },
});
