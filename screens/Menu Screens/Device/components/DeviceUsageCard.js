import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import {
    CustomButton,
    SimpleSkeletonCard,
    SimpleSkeletonText
} from 'components/CustomAction';
import { useNavigation } from '@react-navigation/native';
import formatDuration from '../utils/formartDuration';

const DeviceUsageCard = ({ timerEntry, isLoading = false, disabled }) => {
    const navigation = useNavigation();

    // Format time for display (e.g., "2:30 PM")
    const formatTimeForDisplay = (date) => {
        if (!date) return '';
        return new Date(date).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        }).toLowerCase();
    };

    // Format date for display (e.g., "Apr 21, 2025")
    const formatDateForDisplay = (date) => {
        if (!date) return '';
        return new Date(date).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    };

    // Check if date is today
    const isToday = (date) => {
        if (!date) return false;
        const today = new Date();
        const compareDate = new Date(date);
        return today.getDate() === compareDate.getDate() &&
            today.getMonth() === compareDate.getMonth() &&
            today.getFullYear() === compareDate.getFullYear();
    };

    // If loading state is true, show skeleton loader
    if (isLoading) {
        return (
            <View style={[styles.usageCard, { backgroundColor: Colors.primaryGreen }]}>
                <View style={styles.usageContent}>
                    <View style={styles.usageHeader}>
                        <SimpleSkeletonText
                            width="80%"
                            height={16}
                            style={{ marginBottom: 8 }}
                            isLoading={true}
                        />
                        <SimpleSkeletonText
                            width="60%"
                            height={32}
                            style={{ marginTop: 4 }}
                            isLoading={true}
                        />
                    </View>
                    <View style={{ justifyContent: "center", gap: 2 }}>
                        <SimpleSkeletonCard
                            width={72}
                            height={32}
                            borderRadius={16}
                            isLoading={true}
                        />
                    </View>
                </View>
            </View>
        );
    }

    // If no timer entry is provided, show empty card
    if (!timerEntry) {
        return (
            <View style={{ backgroundColor: Colors.white }}>
                <Text style={{ color: Colors.black, textAlign: "center", padding: 10 }}>
                    No recent history
                </Text>
            </View>
        );
    }

    return (
        <View style={[styles.usageCard, { backgroundColor: Colors.primaryGreen }]}>
            <View style={styles.usageContent}>
                <View style={styles.usageHeader}>
                    <Text style={styles.usageTimeText}>
                        {isToday(timerEntry.startTime)
                            ? `Today at ${formatTimeForDisplay(timerEntry.startTime)}`
                            : `${formatDateForDisplay(timerEntry.startTime)} at ${formatTimeForDisplay(timerEntry.startTime)}`}
                    </Text>
                    <Text style={styles.usageTitle} numberOfLines={1} ellipsizeMode='tail'>
                        {formatDuration(timerEntry.durationConsumed)}
                    </Text>
                </View>
                <View style={{ justifyContent: "center", gap: 2 }}>
                    <CustomButton
                        title="Edit"
                        style={styles.editButton}
                        backgroundColor={Colors.primaryPurple}
                        onPress={() => navigation.navigate("Edit Timer", { timerEntry })}
                        disabled={disabled}
                        disabledBgColor={Colors.primaryPurple}
                        disabledTextColor={Colors.white}
                    />
                </View>
            </View>
        </View>
    );
};

export default DeviceUsageCard;

const styles = StyleSheet.create({
    usageCard: {
        backgroundColor: Colors.primaryGreen,
        borderRadius: 24,
        padding: 16,
        paddingBottom: 24,
        marginBottom: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    usageContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        gap: 4
    },
    usageHeader: {
        flex: 1,
    },
    usageTimeText: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.lightGreen,
        marginTop: 2,
        marginBottom: 2,
        opacity: 0.9
    },
    usageTitle: {
        fontSize: 32,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.white,
        marginTop: 4
    },
    usageDesc: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.lightGreen,
        marginTop: 2,
        marginBottom: 2,
        marginRight: 8
    },
    editButton: {
        width: 72,
        height: 32,
        paddingVertical: 0,
    },
    durationText: {
        fontSize: 10,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.lightGreen,
        textAlign: "center",
    }
});
