import { FlatList, RefreshControl, ScrollView, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View, AppState } from 'react-native';
import React, { useCallback, useEffect, useState, useRef } from 'react';
import AppLayout from "navigations/components/Layouts/AppLayout";
import { ThemeFonts } from 'constants/theme/fonts';
import { useFocusEffect, useRoute } from '@react-navigation/native';
import { Colors } from 'constants/theme/colors';
import { CustomAlert, CustomButton, CustomSelect } from 'components/CustomAction';
import CustomSelectWithLabel from 'components/CustomAction/CustomSelectWithLabel';
import CustomTimePickerWithLabel from 'components/CustomAction/CustomTimePickerWithLabel';
import { Ionicons } from '@expo/vector-icons';
import useDeviceTimerStore from 'store/deviceTimerStore';
import DeviceUsageCard from '../components/DeviceUsageCard';
import { useAnimatedProps, useSharedValue, withTiming } from 'react-native-reanimated';
import Svg, { Circle } from 'react-native-svg';
import Animated from 'react-native-reanimated';
import Toast from 'react-native-toast-message';
import { deviceTimerService } from 'services/deviceTimerService';
import { Audio } from 'expo-av';
import { TIMER_DURATION_OPTIONS } from 'constants/constants';
import DeviceBarGraph from 'components/Charts/BarGraphs/DeviceBarGraph';
import HighlightCard from "components/CustomCards/HighlightCard";
import formatDuration from '../utils/formartDuration';

// Create animated circle component
const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const DeviceScreen = () => {
    const route = useRoute();
    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
    const [isRefreshing, setIsRefreshing] = useState(false);
    // Separate state for Add More section time selection
    const [selectedEditTimerTime, setSelectedEditTimerTime] = useState(null);
    // Separate state for timer edit time selection
    const [selectedTimeAddMoreTime, setSelectedTimeAddMoreTime] = useState(null);
    const [validationError, setValidationError] = useState(null);
    const [isEnding, setIsEnding] = useState(false);

    const [isAddingTimerData, setIsAddingTimerData] = useState(false);

    // Show More/Less state
    const [expandTimerHistory, setExpandTimerHistory] = useState(false);

    // Sound reference
    const soundRef = useRef(null);

    // Reference to track app state
    const appState = useRef(AppState.currentState);
    const backgroundTimerRef = useRef(null);
    const syncIntervalRef = useRef(null);


    const { } = useDeviceTimerStore(state => state);

    // Timer state from Zustand store
    const {
        timerDuration,
        isTimerRunning,
        isPaused,
        remainingTime,
        timerHistory,
        setTimerDuration,
        setAddMoreTimerDuration,
        startTimer,
        pauseTimer,
        resumeTimer,
        endTimer,
        resetTimer,
        syncTimerWithBackend,
        isLoadingTimerHistory,
        getTimerHistory,
        totalUserTime,
        timerHistoryError,
        deviceTimeGraphError,
        getTimerGraphRecords,
        setDeviceTimerStoreError,
        deviceTimerHighlights,
        getTimerHighlights,
    } = useDeviceTimerStore();

    // Timer interval ref
    const timerIntervalRef = useRef(null);

    // Animation values for the timer circle
    const progress = useSharedValue(1);
    const size = 200;
    const outerStrokeWidth = 3;
    const innerStrokeWidth = 3;
    const outerRadius = (size - outerStrokeWidth) / 2;
    const innerRadius = outerRadius - outerStrokeWidth - 3;
    const innerCircumference = innerRadius * 2 * Math.PI;

    // Format time function (converts seconds to HH:MM:SS or MM:SS)
    const formatTime = (timeInSeconds) => {
        const hours = Math.floor(timeInSeconds / 3600);
        const minutes = Math.floor((timeInSeconds % 3600) / 60);
        const seconds = timeInSeconds % 60;

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    };

    // Handle timer actions
    const handleStartTimer = () => {
        if (isTimerRunning) return;

        startTimer();
        startTimerInterval();

        Toast.show({
            type: 'success',
            text1: 'Timer Started',
            text2: 'Time will run in background until it ends',
            position: 'bottom',
            visibilityTime: 3000,
            autoHide: true,
            bottomOffset: 80,
            hideOnPress: true,
        });
    };

    const handlePauseTimer = () => {
        pauseTimer();
        clearInterval(timerIntervalRef.current);
    };

    const handleResumeTimer = () => {
        resumeTimer();
        startTimerInterval();
    };

    const handleEndTimer = async () => {

        if (isEnding) return;

        try {
            setIsEnding(true);

            const formattedTime = formatDuration(timerDuration * 60 - remainingTime);

            if (timerIntervalRef.current) {
                clearInterval(timerIntervalRef.current);
                timerIntervalRef.current = null;
            }
            playSound().catch(err => console.log('error while playing sound: ', err));

            await endTimer();

            Toast.show({
                type: 'success',
                text1: 'Timer Ended',
                text2: `Your timer has ended. Total time: ${formattedTime}`,
                position: 'bottom',
                visibilityTime: 3000,
                autoHide: true,
                bottomOffset: 80,
                hideOnPress: true,
                onHide: async () => {
                    await stopSound();
                }
            });

            resetTimer();
            await getTimerHistory();
            await getTimerGraphRecords();
            await getTimerHighlights();

            setIsEnding(false);
        } catch (error) {

            setIsEnding(false);

            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: 'Failed to end timer. Please try again.',
                position: 'bottom',
                visibilityTime: 3000,
                autoHide: true,
                bottomOffset: 80,
                hideOnPress: true
            });
        }
    };

    // Start the timer interval
    const startTimerInterval = () => {
        // Clear any existing interval first
        clearInterval(timerIntervalRef.current);

        // Only set up the interval if the timer is running and not paused
        const store = useDeviceTimerStore.getState();
        if (store.isTimerRunning && !store.isPaused) {
            timerIntervalRef.current = setInterval(() => {
                const currentStore = useDeviceTimerStore.getState();

                // Double-check that timer is still running and not paused
                if (currentStore.isTimerRunning && !currentStore.isPaused) {
                    currentStore.updateRemainingTime(Math.max(0, currentStore.remainingTime - 1));

                    // If timer reaches 0, end it
                    if (currentStore.remainingTime <= 0) {
                        clearInterval(timerIntervalRef.current);

                        currentStore.endTimer();
                        currentStore.resetTimer();

                        // Play sound
                        playSound().catch(err => console.log('error while playing sound: ', err));

                        // Refresh timer history with a slight delay to ensure API has processed the end timer request
                        setTimeout(async () => {
                            await getTimerHistory(); // This will update isTimerHistoryLoading inside
                            await getTimerGraphRecords();
                            await getTimerHighlights();
                        }, 500);

                        // Show toast instead of custom alert
                        const formatTotalUsageTime = formatDuration(timerDuration * 60);
                        Toast.show({
                            type: 'success',
                            text1: 'Timer Ended',
                            text2: `Your timer has ended. Total time: ${formatTotalUsageTime}`,
                            position: 'bottom',
                            visibilityTime: 3000,
                            autoHide: true,
                            bottomOffset: 80,
                            hideOnPress: true,
                            onHide: async () => {
                                // Stop sound when toast is hidden
                                await stopSound();

                                // Refresh timer history again after toast is dismissed
                                await getTimerHistory();
                                await getTimerGraphRecords();
                                await getTimerHighlights();
                            }
                        });
                    }
                } else if (currentStore.isPaused) {
                    // If timer got paused, clear the interval
                    clearInterval(timerIntervalRef.current);
                }
            }, 1000);
        }
    };

    // Update progress animation when remaining time changes
    useEffect(() => {
        const totalSeconds = timerDuration * 60;
        const newProgress = remainingTime / totalSeconds;
        progress.value = withTiming(newProgress, { duration: 500 });
    }, [remainingTime, timerDuration]);

    // Periodic sync with backend
    useEffect(() => {
        // If timer is running, set up periodic sync
        if (isTimerRunning) {
            // Set up interval for periodic syncing (every 5 seconds)
            // We don't sync immediately to avoid double API calls when the timer starts
            syncIntervalRef.current = setInterval(() => {
                console.log('syncing with backend');
                syncTimerWithBackend();
            }, 5000);
        }

        return () => {
            // Clean up sync interval
            if (syncIntervalRef.current) {
                clearInterval(syncIntervalRef.current);
                syncIntervalRef.current = null;
            }
        };
    }, [isTimerRunning, isPaused]);

    // Handle app state changes (foreground/background)
    useEffect(() => {
        // Subscribe to AppState changes
        const subscription = AppState.addEventListener('change', nextAppState => {
            // App is going to background
            if (appState.current.match(/active/) && nextAppState.match(/inactive|background/)) {
                // If timer is running and not paused, set up background timer
                if (isTimerRunning && !isPaused) {
                    // Clear the foreground interval
                    if (timerIntervalRef.current) {
                        clearInterval(timerIntervalRef.current);
                    }

                    // Store the current timestamp to calculate elapsed time when app comes back to foreground
                    backgroundTimerRef.current = new Date();
                }
            }
            // App is coming to foreground
            else if (nextAppState === 'active' && appState.current.match(/inactive|background/)) {
                // If timer was running when app went to background
                if (isTimerRunning && !isPaused && backgroundTimerRef.current) {
                    // Calculate elapsed time in seconds
                    const now = new Date();
                    const elapsedSeconds = Math.floor((now - backgroundTimerRef.current) / 1000);

                    // Update the remaining time
                    if (elapsedSeconds > 0) {
                        const store = useDeviceTimerStore.getState();
                        const newRemainingTime = Math.max(0, store.remainingTime - elapsedSeconds);
                        store.updateRemainingTime(newRemainingTime);

                        // If timer reached 0 while in background, end it
                        if (newRemainingTime <= 0) {
                            store.endTimer();
                            // Reset the timer display to show the default duration
                            store.resetTimer();
                        } else {
                            // Restart the interval
                            startTimerInterval();
                        }
                    } else {
                        // Just restart the interval if no significant time passed
                        startTimerInterval();
                    }

                    // Reset background timer reference
                    backgroundTimerRef.current = null;
                }
            }

            // Update the app state reference
            appState.current = nextAppState;
        });

        // Clean up subscription and interval on unmount
        return () => {
            subscription.remove();
            if (timerIntervalRef.current) {
                clearInterval(timerIntervalRef.current);
            }
            // Make sure to stop any playing sounds when component unmounts
            stopSound();
        };
    }, [isTimerRunning, isPaused]);

    // Resume timer if it was running when component was unmounted or app was closed
    // Also refresh timer history if coming back from EditTimerScreen
    useFocusEffect(
        useCallback(() => {
            // If timer is running (and not paused), restart the interval
            if (isTimerRunning && !isPaused) {
                startTimerInterval();
            }

            // Check if we need to refresh the timer history
            if (route.params?.refresh) {
                getTimerHistory();
                // Clear the refresh parameter to avoid unnecessary refreshes
                route.params.refresh = false;
            }

            return () => {
                // Just clear the interval, but don't reset the timer state
                // This allows the timer to continue when app is reopened
                if (timerIntervalRef.current) {
                    clearInterval(timerIntervalRef.current);
                }
            };
        }, [isTimerRunning, isPaused, route.params?.refresh])
    );

    // Animated props for the timer circle
    const animatedProps = useAnimatedProps(() => {
        const strokeDashoffset = innerCircumference * (1 - progress.value);
        return {
            strokeDashoffset
        };
    });

    // Load timer history when component mounts and initialize timer edit time
    useEffect(() => {
        (async () => {
            await getTimerHistory();
            await getTimerGraphRecords();
            await getTimerHighlights();
            //    await getTimerGraphRecords("monthly");
        })();
    }, []);

    // Handle refresh
    const handleRefresh = () => {
        setIsRefreshing(true);
        (async () => {
            await getTimerHistory();
            await getTimerGraphRecords();
            await getTimerHighlights();
            setIsRefreshing(false);
        })();
    };

    // Load and play sound - Enhanced with better error handling
    const playSound = async () => {
        try {
            // First stop and unload any existing sound
            await stopSound();

            console.log('Loading and playing sound...');

            // Load the sound file with error handling
            const { sound } = await Audio.Sound.createAsync(
                require('assets/sound4.wav'),
                { shouldPlay: true, isLooping: true }
            );

            // Set the sound reference
            soundRef.current = sound;

            // Set up an onPlaybackStatusUpdate listener to handle any playback issues
            sound.setOnPlaybackStatusUpdate(status => {
                if (status.didJustFinish) {
                    // If sound somehow finishes (shouldn't with looping), restart it
                    sound.replayAsync().catch(err => console.log('Error replaying sound:', err));
                }
                if (status.error) {
                    console.error('Playback error:', status.error);
                }
            });

            console.log('Sound loaded and playing');
        } catch (error) {
            soundRef.current = null;
        }
    };

    // Stop sound - Enhanced with better error handling
    const stopSound = async () => {
        if (soundRef.current) {
            try {
                // First try to stop the sound
                await soundRef.current.stopAsync().catch(err => console.log('Error stopping sound:', err));

                // Then try to unload it
                await soundRef.current.unloadAsync().catch(err => console.log('Error unloading sound:', err));

                // Clear the reference regardless of success/failure
                soundRef.current = null;
                console.log('Sound stopped and unloaded successfully');
            } catch (error) {
                console.error('Error in stopSound function:', error);
                // Force clear the reference even if there was an error
                soundRef.current = null;
            }
        } else {
            console.log('No sound to stop');
        }
    };

    return (
        <AppLayout>
            <ScrollView
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={isRefreshing}
                        onRefresh={handleRefresh}
                        colors={[Colors.primaryGreen]}
                        progressViewOffset={24}
                    />
                }
                progressViewOffset={24}
                contentContainerStyle={{ flexGrow: 1, paddingBottom: 90 }}
            >
                <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
                    <View style={{ flex: 1, flexDirection: "column", gap: 24, marginBottom: 80 }} >
                        {/* Header Section */}
                        <View style={styles.header}>
                            <Text style={styles.headerTitle}>Device</Text>
                            <Text style={styles.subHeading}>
                                Total Usage Time: {formatDuration(totalUserTime)}
                            </Text>
                        </View>

                        {/* Timer Section */}
                        <View style={styles.timerSection}>
                            <View style={styles.timerContainer}>
                                {/* SVG Circle Timer */}
                                <Svg width={size} height={size} style={styles.timerSvg}>
                                    {/* Outer Circle (static) */}
                                    <Circle
                                        cx={size / 2}
                                        cy={size / 2}
                                        r={outerRadius}
                                        stroke={Colors.lightGreen}
                                        strokeWidth={outerStrokeWidth}
                                        fill="transparent"
                                    />
                                    {/* Inner Circle Progress (animated) */}
                                    <AnimatedCircle
                                        cx={size / 2}
                                        cy={size / 2}
                                        r={innerRadius}
                                        stroke={Colors.primaryGreen}
                                        strokeWidth={innerStrokeWidth}
                                        fill="transparent"
                                        strokeDasharray={innerCircumference}
                                        strokeLinecap="round"
                                        animatedProps={animatedProps}
                                    />
                                </Svg>

                                {/* Timer Display */}
                                <View style={styles.timerTextContainer}>
                                    <View style={styles.timerTextWrapper}>
                                        <Text style={styles.timerText}>{formatTime(remainingTime)}</Text>
                                    </View>
                                    <TouchableOpacity
                                        style={[styles.editButton, isTimerRunning && styles.disabledEditButton]}
                                        onPress={() => !isTimerRunning && setCurrentOpenDropdown('timer')}
                                        disabled={isTimerRunning}
                                    >
                                        <Text style={[styles.editButtonText, isTimerRunning && styles.disabledEditButtonText]}>Edit</Text>
                                    </TouchableOpacity>

                                    {/* Timer Duration Dropdown - only show when timer is not running */}
                                    {currentOpenDropdown === 'timer' && !isTimerRunning && (
                                        <View style={styles.timerDropdown}>
                                            <CustomSelect
                                                options={TIMER_DURATION_OPTIONS}
                                                selectedValue={timerDuration}
                                                onValueChange={async (value) => {
                                                    // Timer is not running, just update the duration locally
                                                    setTimerDuration(value);
                                                    resetTimer();
                                                    setCurrentOpenDropdown(null);
                                                }}
                                                width={120}
                                                triggerStyle={{ paddingVertical: 3, paddingHorizontal: 10 }}
                                                disabled={isTimerRunning}
                                                backgroundColor={Colors.primaryPurple}
                                                textColor={Colors.white}
                                                activeOptionBgColor={Colors.lightPurple}
                                                optionsBgColor={Colors.white}
                                                optionsBorderWidth={2}
                                                optionsBorderRadius={20}
                                                optionsBorderColor={Colors.primaryPurple}
                                                triggerBorderWidth={2}
                                                currentOpenDropdown={currentOpenDropdown}
                                                dropdownId={'timer'}
                                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                                alignDropdown='flex-start'
                                                changeBG={true}
                                            />
                                        </View>
                                    )}
                                </View>

                                {/* Timer Control Buttons */}
                                <View style={styles.timerButtonsContainer}>
                                    {!isTimerRunning ? (
                                        // Start Button
                                        <CustomButton
                                            title="Start"
                                            style={{ width: 92 }}
                                            backgroundColor={Colors.primaryGreen}
                                            onPress={handleStartTimer}
                                        />
                                    ) : (
                                        // When timer is running, show Pause/Resume and End buttons
                                        <>
                                            {isPaused ? (
                                                // Resume Button
                                                <CustomButton
                                                    title="Resume"
                                                    style={{ width: 92 }}
                                                    backgroundColor={Colors.primaryPurple}
                                                    onPress={handleResumeTimer}
                                                />
                                            ) : (
                                                // Pause Button
                                                <CustomButton
                                                    title="Pause"
                                                    style={{ width: 92 }}
                                                    backgroundColor={Colors.primaryPurple}
                                                    onPress={handlePauseTimer}
                                                />
                                            )}

                                            {/* End Button */}
                                            <CustomButton
                                                title={isEnding ? "Ending..." : "End"}
                                                style={{
                                                    width: 92,
                                                    borderWidth: 2,
                                                    borderColor: isEnding ? Colors.gray : Colors.red,
                                                    opacity: isEnding ? 0.7 : 1
                                                }}
                                                textColor={isEnding ? Colors.gray : Colors.red}
                                                backgroundColor={Colors.white}
                                                onPress={handleEndTimer}
                                                disabled={isEnding}
                                            />
                                        </>
                                    )}
                                </View>
                            </View>
                        </View>

                        {/* Recents Section */}
                        <View style={styles.recentsSection}>
                            <Text style={styles.sectionHeading}>Recent History</Text>

                            {isLoadingTimerHistory ? (
                                // Show skeleton loaders when loading
                                <>
                                    <DeviceUsageCard isLoading={true} />
                                </>
                            ) : timerHistory.length === 0 ? (
                                <DeviceUsageCard timerEntry={null} />
                            ) : (
                                <>
                                    <FlatList
                                        data={expandTimerHistory ? timerHistory : timerHistory.slice(0, 3)}
                                        keyExtractor={(item) => item.id}
                                        scrollEnabled={false}
                                        renderItem={({ item }) => (
                                            <DeviceUsageCard timerEntry={item} disabled={isTimerRunning || isPaused} />
                                        )}
                                    />

                                    {/* Show More/Less Button */}
                                    {timerHistory.length > 3 && (
                                        <TouchableOpacity
                                            activeOpacity={0.5}
                                            onPress={() => setExpandTimerHistory(prev => !prev)}
                                            style={styles.showMoreContainer}
                                        >
                                            <Text style={styles.showMoreText}>
                                                {expandTimerHistory ? 'Show Less' : 'Show More'}
                                            </Text>
                                            <Ionicons
                                                name={expandTimerHistory ? "chevron-up" : "chevron-down"}
                                                size={16}
                                                color={Colors.primaryPurple}
                                                style={{ marginLeft: 5 }}
                                            />
                                        </TouchableOpacity>
                                    )}
                                </>
                            )}
                        </View>

                        {/* Graph Section */}
                        <DeviceBarGraph currentOpenDropdown={currentOpenDropdown} setCurrentOpenDropdown={setCurrentOpenDropdown} disabled={isTimerRunning && !isPaused} />
                        <HighlightCard highlightData={deviceTimerHighlights} />

                        {/* Add More Section */}
                        <View style={styles.addMoreSection}>
                            <Text style={styles.sectionHeading}>Add More</Text>
                            <View style={styles.formWrapper}>
                                <View style={{ gap: 24 }}>
                                    <CustomTimePickerWithLabel
                                        label="Time"
                                        selectedTime={selectedEditTimerTime}
                                        onChangeTime={(value) => {
                                            console.log(value);
                                            setSelectedEditTimerTime(value);
                                            if (value === null) {
                                                setValidationError(prev => ({
                                                    ...prev,
                                                    timeError: "Cannot select future time"
                                                }));
                                            }
                                        }}
                                        disabled={isTimerRunning}
                                        error={validationError?.timeError}
                                        onPress={() => setCurrentOpenDropdown(null)}
                                        clearValidationError={() => {
                                            setValidationError((prev) => ({ ...prev, timeError: "" }));
                                        }}
                                        maxTime={new Date()} // Prevent selecting future times
                                    />

                                    <CustomSelectWithLabel
                                        options={TIMER_DURATION_OPTIONS}
                                        label="Duration"
                                        selectedValue={selectedTimeAddMoreTime}
                                        onValueChange={(value) => {
                                            console.log(value);
                                            // Only update duration locally if timer is not running
                                            if (!isTimerRunning) {
                                                setSelectedTimeAddMoreTime(value);
                                            }
                                        }}
                                        triggerZ={10}
                                        listZ={9}
                                        isEditing={!isTimerRunning}
                                        currentOpenDropdown={currentOpenDropdown}
                                        dropdownId={4}
                                        setCurrentOpenDropdown={(id) => isTimerRunning ? null : setCurrentOpenDropdown(id)}
                                        error={validationError?.durationError}
                                        clearValidationError={() => {
                                            setValidationError((prev) => ({ ...prev, durationError: "" }));
                                        }}
                                        disabledSelectColor={Colors.lightGray}
                                        disabledTextColor={Colors.black}
                                    />
                                </View>
                            </View>

                            <View style={styles.buttonContainer}>
                                <CustomButton
                                    title="Save"
                                    style={{
                                        width: "auto",
                                        paddingHorizontal: 24,
                                        opacity: isTimerRunning || isAddingTimerData ? 0.5 : 1
                                    }}
                                    backgroundColor={Colors.primaryPurple}
                                    disabled={isTimerRunning || isAddingTimerData}
                                    onPress={async () => {
                                        // Only allow creating new timer entries when no timer is running
                                        if (!isTimerRunning) {
                                            // Validate inputs
                                            if (!selectedEditTimerTime) {
                                                setValidationError(prev => ({ ...prev, timeError: "Please select a time" }));
                                                return;
                                            }

                                            if (!selectedTimeAddMoreTime) {
                                                setValidationError(prev => ({ ...prev, durationError: "Please select a duration" }));
                                                return;
                                            }

                                            setIsAddingTimerData(true);

                                            // Create a new timer entry with the selected time
                                            const today = new Date();
                                            const selectedDateTime = new Date(selectedEditTimerTime);

                                            // Set the date to today, keeping the selected time
                                            const startTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(),
                                                selectedDateTime.getHours(), selectedDateTime.getMinutes());

                                            const historyEntry = {
                                                id: Date.now().toString(),
                                                startTime: startTime,
                                                endTime: new Date(startTime.getTime() + selectedTimeAddMoreTime * 60 * 1000),
                                                duration: selectedTimeAddMoreTime,
                                                completed: true
                                            };

                                            // Send to backend using the add_more endpoint
                                            try {
                                                const response = await deviceTimerService.addMoreTimer({
                                                    startTime: historyEntry.startTime,
                                                    durationConsumed: historyEntry.duration * 60 // Convert minutes to seconds for the API
                                                });

                                                if (response.success) {
                                                    // Show success toast
                                                    const successMsg = `Added ${selectedTimeAddMoreTime} min timer at ${startTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}`;
                                                    Toast.show({
                                                        type: 'success',
                                                        text1: 'Success',
                                                        text2: successMsg,
                                                        position: 'bottom',
                                                        visibilityTime: 3000,
                                                        autoHide: true,
                                                        bottomOffset: 80,
                                                        hideOnPress: true
                                                    });

                                                    /* // Show success alert with dynamic props - COMMENTED OUT
                                                    setAlertConfig({
                                                        visible: true,
                                                        title: "Success",
                                                        message: successMsg,
                                                        buttons: [
                                                            {
                                                                text: "OK",
                                                                onPress: () => setAlertConfig(prev => ({ ...prev, visible: false })),
                                                            }
                                                        ],
                                                        onClose: () => setAlertConfig(prev => ({ ...prev, visible: false }))
                                                    });
                                                    */

                                                    // Reset form
                                                    setSelectedEditTimerTime(null);
                                                    resetTimer();

                                                    // Refresh timer history
                                                    await getTimerHistory();
                                                    setIsAddingTimerData(false);

                                                    await getTimerGraphRecords();
                                                    await getTimerHighlights();
                                                } else {
                                                    // Show error toast
                                                    Toast.show({
                                                        type: 'error',
                                                        text1: 'Error',
                                                        text2: response.error || 'Failed to save timer. Please try again.',
                                                        position: 'bottom',
                                                        visibilityTime: 3000,
                                                        autoHide: true,
                                                        bottomOffset: 80,
                                                        hideOnPress: true
                                                    });

                                                    /* // Show error alert with dynamic props - COMMENTED OUT
                                                    setAlertConfig({
                                                        visible: true,
                                                        title: "Error",
                                                        message: response.error || 'Failed to save timer. Please try again.',
                                                        buttons: [
                                                            {
                                                                text: "OK",
                                                                onPress: () => setAlertConfig(prev => ({ ...prev, visible: false })),
                                                            }
                                                        ],
                                                        onClose: () => setAlertConfig(prev => ({ ...prev, visible: false }))
                                                    });
                                                    */

                                                    setIsAddingTimerData(false);
                                                }
                                            } catch (error) {
                                                console.error('Failed to send timer data to API:', error);
                                                // Show error toast
                                                Toast.show({
                                                    type: 'error',
                                                    text1: 'Error',
                                                    text2: 'Failed to save timer. Please try again.',
                                                    position: 'bottom',
                                                    visibilityTime: 3000,
                                                    autoHide: true,
                                                    bottomOffset: 80,
                                                    hideOnPress: true
                                                });

                                                /* // Show error alert with dynamic props - COMMENTED OUT
                                                setAlertConfig({
                                                    visible: true,
                                                    title: "Error",
                                                    message: 'Failed to save timer. Please try again.',
                                                    buttons: [
                                                        {
                                                            text: "OK",
                                                            onPress: () => setAlertConfig(prev => ({ ...prev, visible: false })),
                                                        }
                                                    ],
                                                    onClose: () => setAlertConfig(prev => ({ ...prev, visible: false }))
                                                });
                                                */

                                                setIsAddingTimerData(false);
                                            }
                                        }
                                    }}
                                />
                            </View>
                        </View>

                    </View>
                </TouchableWithoutFeedback>
            </ScrollView>

            {/* Single Dynamic Alert - Commented out in favor of Toast
            <CustomAlert
                visible={alertConfig.visible}
                title={alertConfig.title}
                message={alertConfig.message}
                buttons={alertConfig.buttons}
                onClose={alertConfig.onClose}
            />
            */}
            <CustomAlert
                visible={!!deviceTimeGraphError}
                title={"Error"}
                message={deviceTimeGraphError}
                buttons={[
                    {
                        text: "OK",
                        onPress: () => setDeviceTimerStoreError(null),
                    }
                ]}
                onClose={() => setDeviceTimerStoreError(null)}
            />
        </AppLayout>
    );
};

export default DeviceScreen;

const styles = StyleSheet.create({
    header: {
        marginHorizontal: 22,
        marginVertical: 10
    },
    headerTitle: {
        fontSize: 35,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700
    },
    subHeading: {
        fontSize: 20,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_500,
        marginTop: 5
    },
    timerSection: {
        alignItems: 'center',
        marginTop: 10
    },
    timerContainer: {
        position: 'relative',
        alignItems: 'center',
        justifyContent: 'center',
        width: 220,
        height: 220
    },
    timerSvg: {
        position: 'absolute',
        transform: [{ rotate: '-90deg' }]
    },
    timerTextContainer: {
        alignItems: 'center',
        justifyContent: 'center'
    },
    timerTextWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        // justifyContent: 'space-between',
        // width: 100,
        height: 'auto'
    },
    timerText: {
        fontSize: 40,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.black
    },
    timerLabel: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.black,
        marginHorizontal: 5,
        marginTop: 15
    },
    editButton: {
        marginTop: 5,
        padding: 5
    },
    editButtonText: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.black,
        textDecorationLine: 'underline'
    },
    disabledEditButton: {
        opacity: 0.5,
    },
    disabledEditButtonText: {
        color: Colors.darkGray,
        textDecorationLine: 'underline'
    },
    timerDropdown: {
        position: 'absolute',
        top: 60,
        zIndex: 10
    },
    timerButtonsContainer: {
        position: 'absolute',
        bottom: -40,
        flexDirection: 'row',
        justifyContent: 'center',
        gap: 5
    },
    timerButton: {
        paddingHorizontal: 10,
        paddingVertical: 5,
        borderRadius: 20,
        elevation: 2,
        minWidth: 70,
        alignItems: 'center'
    },
    startButton: {
        backgroundColor: Colors.primaryGreen
    },
    pauseButton: {
        backgroundColor: Colors.primaryPurple
    },
    resumeButton: {
        backgroundColor: Colors.primaryPurple
    },
    endButton: {
        backgroundColor: Colors.red
    },
    timerButtonText: {
        color: Colors.white,
        fontFamily: ThemeFonts.Exo_600,
        fontSize: 16
    },
    recentsSection: {
        marginHorizontal: 10,
        marginTop: 10
        // marginVertical: 10
    },
    sectionHeading: {
        fontSize: 32,
        fontFamily: ThemeFonts.Exo_600,
        color: Colors.black,
        marginVertical: 20,
        marginHorizontal: 10
    },
    addMoreSection: {
        marginHorizontal: 10,
        marginBottom: 20
    },
    formWrapper: {
        flex: 1,
        marginBottom: 20,
        gap: 10,
        flexDirection: "column",
        justifyContent: "space-between",
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        marginTop: 10,
        // marginRight: 16,
        marginBottom: 20
    },
    loaderContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: 'center',
    },
    disabledText: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.primaryPurple,
        marginRight: 10,
        fontStyle: 'italic'
    },
    showMoreContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 30,
        marginTop: 10,
        marginBottom: 20
    },
    showMoreText: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_600,
        color: Colors.primaryPurple
    }
});