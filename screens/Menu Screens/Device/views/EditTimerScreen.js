import {
  StyleSheet,
  Text,
  View,
  TouchableWithoutFeedback,
  ScrollView,
} from "react-native";
import React, { useState, useRef } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import {
  CustomAlert,
  CustomButton,
  CustomLoader,
} from "components/CustomAction";
import CustomTimePickerWithLabel from "components/CustomAction/CustomTimePickerWithLabel";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import { useNavigation, useRoute } from "@react-navigation/native";
import { deviceTimerService } from "services/deviceTimerService";
// Removed Toast import
import { TIMER_DURATION_OPTIONS } from "constants/constants";
import useDeviceTimerStore from "store/deviceTimerStore";

const EditTimerScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const scrollViewRef = useRef();

  // Get timer entry from route params
  const { timerEntry } = route.params;

  const { getTimerHistory } = useDeviceTimerStore();

  const [isLoading, setIsLoading] = useState(false);
  const [selectedTime, setSelectedTime] = useState(timerEntry.startTime);
  const [selectedDuration, setSelectedDuration] = useState(
    timerEntry.durationConsumed
  );
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [validationError, setValidationError] = useState(null);
  const [error, setError] = useState(null);
  const [showAlert, setShowAlert] = useState(false);

  // Validate inputs
  const validateData = () => {
    if (!selectedTime) {
      setValidationError({
        timeError: "Please select a time",
      });
      return false;
    }

    if (!selectedDuration) {
      setValidationError({
        durationError: "Please select a duration",
      });
      return false;
    }

    setValidationError(null);
    return true;
  };

  // Handle save button press
  const handleSaveTimer = async () => {
    setCurrentOpenDropdown(null);

    if (!validateData()) return;

    setIsLoading(true);

    try {
      // Set the date to the original date, keeping the selected time
      const originalDate = new Date(timerEntry.startTime);
      const selectedDateTime = new Date(selectedTime);

      const startTime = new Date(
        originalDate.getFullYear(),
        originalDate.getMonth(),
        originalDate.getDate(),
        selectedDateTime.getHours(),
        selectedDateTime.getMinutes()
      );

      // Update timer in the backend
      const response = await deviceTimerService.updateAddMoreTimer({
        id: timerEntry.id,
        startTime: startTime,
        durationConsumed: selectedDuration * 60, // Convert minutes to seconds for the API
      });

      if (response.success) {
        // Show success alert
        setShowAlert(true);

        // Wait for the user to dismiss the alert before navigating back
        // The navigation will happen when the user clicks OK on the alert
      } else {
        setError(response.error || "Failed to update timer. Please try again.");
      }
    } catch (error) {
      console.error("Error updating timer:", error);
      setError("An error occurred while updating the timer. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
        <View style={{ flex: 1, flexDirection: "column", marginBottom: 80 }}>
          <ScrollView ref={scrollViewRef} showsVerticalScrollIndicator={false}>
            <CustomAlert
              visible={!!error}
              title={"Error"}
              message={error}
              buttons={[
                {
                  text: "OK",
                  onPress: () => setError(null),
                },
              ]}
              onClose={() => setError(null)}
            />

            <CustomAlert
              visible={showAlert}
              title={"Success"}
              message={`Timer updated successfully to ${selectedDuration} min at ${new Date(
                selectedTime
              ).toLocaleTimeString("en-US", {
                hour: "2-digit",
                minute: "2-digit",
                hour12: true,
              })}`}
              buttons={[
                {
                  text: "OK",
                  onPress: () => {
                    setShowAlert(false);
                  },
                },
              ]}
              onClose={() => {
                setShowAlert(false);
                getTimerHistory();
                navigation.goBack();
              }}
            />
            <TouchableWithoutFeedback
              onPress={() => setCurrentOpenDropdown(null)}
            >
              <View style={styles.formWrapper}>
                <View style={{ flex: 1 }}>
                  <View style={styles.header}>
                    <Text style={styles.headerText}>Edit Timer</Text>
                  </View>
                  <View style={{ gap: 24, marginTop: 8 }}>
                    <CustomTimePickerWithLabel
                      label="Time"
                      selectedTime={selectedTime}
                      onChangeTime={(value) => {
                        setSelectedTime(value);
                        // If value is null (invalid time), set validation error
                        if (value === null) {
                          setValidationError((prev) => ({
                            ...prev,
                            timeError: "Cannot select future time",
                          }));
                        }
                      }}
                      error={validationError?.timeError}
                      onPress={() => setCurrentOpenDropdown(null)}
                      clearValidationError={() => {
                        setValidationError((prev) => ({
                          ...prev,
                          timeError: "",
                        }));
                      }}
                      maxTime={new Date()} // Prevent selecting future times
                    />

                    <CustomSelectWithLabel
                      options={TIMER_DURATION_OPTIONS}
                      label="Duration"
                      selectedValue={selectedDuration}
                      onValueChange={(value) => setSelectedDuration(value)}
                      triggerZ={10}
                      listZ={9}
                      currentOpenDropdown={currentOpenDropdown}
                      dropdownId={1}
                      setCurrentOpenDropdown={(id) =>
                        setCurrentOpenDropdown(id)
                      }
                      error={validationError?.durationError}
                      clearValidationError={() => {
                        setValidationError((prev) => ({
                          ...prev,
                          durationError: "",
                        }));
                      }}
                    />
                  </View>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </ScrollView>
          <View style={styles.buttonContainer}>
            <CustomButton
              title={"Cancel"}
              onPress={() => navigation.goBack()}
              style={{
                width: 84,
                backgroundColor: Colors.white,
                borderWidth: 2,
                borderColor: Colors.primaryPurple,
              }}
              textColor={Colors.primaryPurple}
            />
            <CustomButton
              title={"Save"}
              onPress={handleSaveTimer}
              style={{
                width: 84,
              }}
              backgroundColor={Colors.primaryPurple}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </AppLayout>
  );
};

export default EditTimerScreen;

const styles = StyleSheet.create({
  formWrapper: {
    flex: 1,
    marginBottom: 20,
    gap: 10,
    flexDirection: "column",
    justifyContent: "space-between",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    marginHorizontal: 16,
  },
  headerText: {
    fontSize: 32,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
    textTransform: "capitalize",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 10,
    // marginRight: 16,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});
