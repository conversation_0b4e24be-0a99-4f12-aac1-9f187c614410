import { useNavigation, useRoute } from "@react-navigation/native";
import {
  <PERSON><PERSON>lert,
  CustomButton,
  CustomLoader,
} from "components/CustomAction";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { screenHeight } from "constants/sizes";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import React, { useState, useRef, useEffect, lazy, Suspense } from "react";
import { TouchableWithoutFeedback } from "react-native";
import {
  StyleSheet,
  View,
  ScrollView,
  Text,
} from "react-native";
import { recordSleepService } from "services/recordSleepService";
import useSleepStore from "store/sleepStore";

// const CustomCalender = lazy(() => import('components/CustomPickers/CustomCalender'));

const sleepHoursOptions = [
  {
    label: 4,
    value: 4,
  },
  {
    label: 4.5,
    value: 4.5,
  },
  {
    label: 5,
    value: 5,
  },
  {
    label: 5.5,
    value: 5.5,
  },
  {
    label: 6,
    value: 6,
  },
  {
    label: 6.5,
    value: 6.5,
  },
  {
    label: 7,
    value: 7,
  },
  {
    label: 7.5,
    value: 7.5,
  },
  {
    label: 8,
    value: 8,
  },
];

const CalendarScreen = () => {
  const scrollViewRef = useRef();
  const navigation = useNavigation();

  const {
    setLoadingSleepStore,
    getLastLoggedSleep,
    getSleepGraphRecords,
    getSleepHighLights,
  } = useSleepStore((state) => state);

  const [selected, setSelected] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [selectedSleepHours, setSelectedSleepHours] = useState(null);

  const [isLoadingSleepData, setIsLoadingSleepData] = useState(false);
  const [sleepData, setSleepData] = useState([]);

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [validationError, setValidationError] = useState({});

  const [CustomCalender, setCustomCalender] = useState(null);

  const loadComponent = async () => {
    const module = await import('components/CustomPickers/CustomCalender');
    setCustomCalender(() => module.default);
  };

  useEffect(() => {
    loadComponent();
  }, [])


  const getSleepRecord = async () => {
    setIsLoadingSleepData(true);
    const res = await recordSleepService.getSleepRecord(selected);
    if (res.success) {
      setSleepData(res.data);
      setSelectedSleepHours(res.data ? res.data.numOfHours : null);
    } else {
      setError(res.error);
    }
    setIsLoadingSleepData(false);
  }

  useEffect(() => {
    getSleepRecord();
  }, [selected]);

  const handleLogSleep = async () => {
    if (!selectedSleepHours) {
      setValidationError({
        duration: "Please select sleep hours.",
      });
      return;
    }
    setIsLoading(true);
    setValidationError({});
    const res = await recordSleepService.logSleepData({
      date: selected,
      duration: selectedSleepHours,
    });

    if (res.success) {
      setLoadingSleepStore(true);
      await getLastLoggedSleep();
      await getSleepGraphRecords();
      await getSleepHighLights();
      await getSleepRecord();
      setLoadingSleepStore(false);
      setSuccess(res.message);
    } else setError(res.error);

    setIsLoading(false);
  };

  const scrollToOffset = (y) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: true });
  };

  if (!CustomCalender) {
    return (
      <AppLayout bgColor={Colors.primaryGreen} paddingHorizontal={0} paddingTop={20}>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    )
  }

  if (isLoading) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    // <Suspense>
    <AppLayout bgColor={Colors.primaryGreen} paddingHorizontal={0} paddingTop={20}>
      <ScrollView showsVerticalScrollIndicator={false} ref={scrollViewRef} contentContainerStyle={{ flexGrow: 1 }}>
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View style={styles.container}>
            <CustomCalender
              heading="Sleep"
              totalDays={30}
              currentMonth={selected}
              setCurrentMonth={(value) => setSelected(value)}
              currentOpenDropdown={currentOpenDropdown}
              setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
              clearValidationError={() => {
                setValidationError((prev) => ({ ...prev, duration: "" }));
              }}
            />
            <View style={[styles.section2, { paddingBottom: sleepData ? 90 : 400 }]}>
              {/* {
                isLoadingSleepData && (
                  <View style={[styles.loaderContainer, { bottom: 0, paddingVertical: 16 }]}>
                    <CustomLoader small={true} />
                  </View>
                )

              } */}
              <View style={styles.header}>
                <Text style={styles.recordSleep}>Record Sleep</Text>
                {sleepData && (
                  <Text style={styles.alreadyLogged}>{"Already logged"}</Text>
                )}
              </View>
              <View style={styles.section1}>
                <CustomSelectWithLabel
                  label="No. of hours"
                  options={sleepHoursOptions}
                  selectedValue={selectedSleepHours}
                  onValueChange={(value) => setSelectedSleepHours(value)}
                  scrollToOffset={scrollToOffset}
                  currentOpenDropdown={currentOpenDropdown}
                  error={validationError?.duration}
                  dropdownId={"sleep_hours_dropdown"}
                  setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                  disabled={isLoadingSleepData}
                  clearValidationError={() => {
                    setValidationError((prev) => ({ ...prev, duration: "" }));
                  }}
                  isEditing={!sleepData}
                  // disabledSelectColor={Colors.lightGray}
                  // disabledTextColor={Colors.black}
                  showSaveIcon={true}
                  isLoading={isLoadingSleepData}
                />
              </View>
              {!sleepData && <CustomButton
                title="Save"
                disabled={isLoadingSleepData}
                style={{ alignSelf: "flex-end", width: "auto", paddingHorizontal: 28 }}
                onPress={handleLogSleep}
                disabledBgColor={Colors.primaryPurple}
                disabledTextColor={Colors.white}
                isLoading={isLoadingSleepData}
              />}
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
      <CustomAlert
        visible={!!error || !!success}
        title={error ? "Error" : "Success"}
        message={error || success}
        buttons={[{ text: "OK", onPress: () => { }, style: "allowButton" }]}
        onClose={() => {
          setError(null);
          setSuccess(null);
          navigation.goBack();
        }}
      />
    </AppLayout>
    // </Suspense>
  );
};

export default CalendarScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // top: -10,
    // marginHorizontal: 16
  },
  section2: {
    flex: 1,
    flexDirection: "column",
    gap: 24,
    backgroundColor: Colors.white,
    borderTopRightRadius: 50,
    borderTopLeftRadius: 50,
    padding: 16,
    marginTop: 16,
  },
  heading: {
    fontSize: 35,
    color: Colors.white,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
    marginHorizontal: 24,
    // marginBottom: 10,
  },
  text: {
    textAlign: "center",
    padding: 10,
    backgroundColor: "lightgrey",
    fontSize: 16,
  },
  customCalendar: {
    height: screenHeight * 0.3,
    borderBottomWidth: 1,
    borderBottomColor: "lightgrey",
  },
  customDay: {
    textAlign: "center",
  },
  customHeader: {
    backgroundColor: Colors.primaryGreen,
    flexDirection: "col",
    // marginHorizontal: -4,
    padding: 8,
  },
  customWeekDaysContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    marginTop: 4,
  },
  weekDayName: {
    color: Colors.white,
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_500,
  },
  header: {
    marginHorizontal: 22,
    top: 10,
  },
  recordSleep: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  alreadyLogged: {
    fontSize: 18,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
  subHeading: {
    fontSize: 25,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
  lastLogTimeText: {
    fontSize: 12,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
  section1: {
    marginTop: 16,
  },
  section1Heading: {
    fontSize: 19,
    color: Colors.veryLightGreen,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  highLightsText: {
    fontSize: 18,
    color: Colors.white,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_400,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});
