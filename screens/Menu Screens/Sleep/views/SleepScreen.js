import {
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useState,
} from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { ThemeFonts } from "constants/theme/fonts";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { Colors } from "constants/theme/colors";
import {
  CustomAlert,
  CustomButton,
  CustomLoader,
} from "components/CustomAction";
import useSleepStore from "store/sleepStore";
import SleepLineGraph from "components/Charts/LineGraphs/SleepLineGraph";
import HighlightCard from "components/CustomCards/HighlightCard";
import useGlobalLoadingState from "store/globalLoadingState";
import getRecentLogTime from "utils/dateandtimeformatters/getRecentLogTime";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const SleepScreen = () => {
  const navigation = useNavigation();

  const {
    isLoadingLastSleepLogged,
    isLoadingSleepData,
    lastLoggedSleep,
    isLoadingSleepHighLights,
    sleepHighlights,
    sleepError,
    sleepGraphRecordError,
    sleepHightlightsError,
  } = useSleepStore((state) => state);
  const {
    setLoadingSleepStore,
    getLastLoggedSleep,
    getSleepGraphRecords,
    getSleepHighLights,
    clearSleepStoreErrors,
  } = useSleepStore((state) => state);

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

  useEffect(() => {
    // setLoadingSleepStore(true);
    (async () => {
      getLastLoggedSleep();
      getSleepGraphRecords();
      getSleepHighLights();
      // setLoadingSleepStore(false);
    })();
  }, []);

  if (isLoadingSleepData) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }


  return (
    <AppLayout>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            onRefresh={async () => {
              // setLoadingSleepStore(true);
              getLastLoggedSleep();
              getSleepGraphRecords();
              getSleepHighLights();
              // setLoadingSleepStore(false);
            }}
            colors={[Colors.primaryGreen]}
            progressViewOffset={24}
            refreshing={isLoadingSleepData}
          />
        }
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 90 }}
      >
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View style={{ flex: 1, flexDirection: "column", gap: 24 }}>
            <View style={styles.header}>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Text style={styles.headerTitle}>Sleep</Text>
                <CustomButton
                  title="Log"
                  onPress={() => {
                    navigation.navigate("Record_Sleep");
                  }}
                  style={{ width: 78 }}
                />
              </View>
              {!sleepError && (
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                    marginTop: 2,
                  }}
                >

                  <SkeletonItem isLoading={isLoadingLastSleepLogged} height={28} width={'45%'} style={{ marginTop: 0 }} >
                    {
                      lastLoggedSleep && (
                        <Text
                          style={styles.subHeading}
                        >{`Last Log ${lastLoggedSleep?.numOfHours} hrs`}</Text>
                      )
                    }
                  </SkeletonItem>
                  <SkeletonItem isLoading={isLoadingLastSleepLogged} height={18} width={'30%'} style={{ marginTop: 0 }} >
                    {
                      lastLoggedSleep && (
                        <Text style={styles.lastLogTimeText}>
                          {getRecentLogTime(
                            lastLoggedSleep?.createdAt,
                            lastLoggedSleep?.date
                          )}
                        </Text>
                      )
                    }
                  </SkeletonItem>
                  {/* <Text>{getDayName(lastLoggedSleep.createdAt, lastLoggedSleep.date)}</Text> */}
                </View>
              )}
            </View>
            <View style={styles.graphSection}>
              <SleepLineGraph
                currentOpenDropdown={currentOpenDropdown}
                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
              />
            </View>
            <HighlightCard isLoading={isLoadingSleepHighLights} highlightData={sleepHighlights} />
            <CustomAlert
              title="Error"
              message={
                sleepError || sleepGraphRecordError || sleepHightlightsError
              }
              visible={
                !!sleepError ||
                !!sleepGraphRecordError ||
                !!sleepHightlightsError
              }
              buttons={[{ text: "OK", onPress: clearSleepStoreErrors }]}
              onClose={clearSleepStoreErrors}
            />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default SleepScreen;

const styles = StyleSheet.create({
  header: {
    marginHorizontal: 22,
    top: 10,
  },
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  subHeading: {
    fontSize: 20,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
  lastLogTimeText: {
    fontSize: 12,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
  highlightSection: {
    backgroundColor: Colors.primaryGreen,
    marginHorizontal: 8,
    padding: 22,
    borderRadius: 25,
    paddingTop: 16,
  },
  highlightSectionHeading: {
    fontSize: 19,
    color: Colors.veryLightGreen,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  highLightsText: {
    fontSize: 18,
    color: Colors.white,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_400,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});
