import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { CustomButton } from 'components/CustomAction'
import { Colors } from 'constants/theme/colors'
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { activityService } from 'services/activityService';
import { ThemeFonts } from 'constants/theme/fonts';

const ActivityOverviewCard = ({ activity }) => {
    const navigation = useNavigation();

    return (
        <View key={activity.id} style={styles.activityCard}>
            <View style={styles.activityContent}>
                <View style={styles.activityHeader}>
                    <Text style={styles.activityTitle}>{activity.title}</Text>
                    <Text style={styles.activityTime}>
                        {activity.totalCalories} kcal
                    </Text>
                </View>
                <CustomButton
                    title="Edit"
                    onPress={() => navigation.navigate("Edit Activity", { id: activity.id })}
                    style={styles.editButton}
                    fontFamily="Exo_600SemiBold"
                />
            </View>
        </View>
    )
}

export default ActivityOverviewCard

const styles = StyleSheet.create({
    activityCard: {
        backgroundColor: Colors.primaryGreen,
        borderRadius: 24,
        padding: 18,
        paddingHorizontal: 25,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    activityContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    activityHeader: {
        flex: 1,
    },
    activityTitle: {
        fontSize: 30,
        color: Colors.white,
        fontFamily: ThemeFonts.Exo_400,
        marginBottom: 2,
        textTransform: "capitalize"
    },
    activityTime: {
        fontSize: 18,
        fontFamily: ThemeFonts.Lexend_600,
        color: Colors.lightGreen,
    },
    editButton: {
        maxWidth: 72
    },
    deleteButton: {
        marginLeft: 12,
        backgroundColor: Colors.white,
        padding: 8,
        borderRadius: 8,
    }
})