import {
  FlatList,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useState, useEffect, useRef } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import {
  CustomAlert,
  CustomButton,
} from "components/CustomAction";
import { useNavigation, useRoute } from "@react-navigation/native";
import { ACTIVITY_TYPES, DURATIONS } from "../constants";
import useActivityStore from "store/activityStore";
import ActivityOverviewCard from "../components/ActivityOverviewCard";
import { SkeletonList } from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const EditActivitiesScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();

  const scrollViewRef = useRef();

  // Activity store
  const {
    saveActivity,
    isSavingActivity,
    savingActivityError,
    clearActivityStoreErrors,
    allActivities,
    totalBurnedCalories,
    isLoadingAllActivities,
    getAllActivities
  } = useActivityStore();

  const [selectedType, setSelectedType] = useState(null);
  const [selectedDuration, setSelectedDuration] = useState(null);
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [validationError, setValidationError] = useState(null);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);

  useEffect(() => {
    if (route.params?.id) {
      // Add your fetch logic here for editing existing activity
    }
  }, [route.params?.id]);

  // Fetch all activities when component mounts
  useEffect(() => {
    getAllActivities();
  }, []);

  // Validation function
  const validateData = () => {
    if (!selectedType) {
      setValidationError("Please select an activity type");
      return false;
    }

    if (!selectedDuration) {
      setValidationError("Please select a duration");
      return false;
    }

    setValidationError(null);
    return true;
  };

  const handleSaveActivity = async () => {
    // Clear previous errors
    clearActivityStoreErrors();
    setValidationError(null);

    // Validate input
    if (!validateData()) {
      return;
    }

    try {
      const res = await saveActivity({
        activityType: selectedType,
        durationInMinutes: selectedDuration,
      });

      if (res.success) {
        setShowSuccessAlert(true);
        // Navigate back after showing success alert
        setTimeout(() => {
          setShowSuccessAlert(false);
          navigation.goBack();
        }, 2000);
      } else {
        setShowErrorAlert(true);
      }
    } catch (error) {
      setShowErrorAlert(true);
    }
  };
  const scrollToOffset = (y) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: true });
  };

  // Show skeleton loader while saving activity
  if (isSavingActivity) {
    return (
      <AppLayout>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Add Activity</Text>
          </View>
          <View style={styles.content}>
            <SkeletonList items={4} height={60} gap={20} />
          </View>
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <ScrollView showsVerticalScrollIndicator={false} ref={scrollViewRef} contentContainerStyle={{ flexGrow: 1, paddingBottom: 130 }} co>
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View
            style={{ paddingHorizontal: 8, paddingTop: 8, paddingBottom: 200 }}
          >
            {/* Success Alert */}
            <CustomAlert
              visible={showSuccessAlert}
              title={"Success"}
              message={"Activity saved successfully!"}
              buttons={[{ text: "OK", onPress: () => setShowSuccessAlert(false) }]}
              onClose={() => setShowSuccessAlert(false)}
            />

            {/* Error Alert */}
            <CustomAlert
              visible={showErrorAlert || !!savingActivityError || !!validationError}
              title={"Error"}
              message={validationError || savingActivityError || "Failed to save activity"}
              buttons={[{
                text: "OK", onPress: () => {
                  setShowErrorAlert(false);
                  setValidationError(null);
                  clearActivityStoreErrors();
                }
              }]}
              onClose={() => {
                setShowErrorAlert(false);
                setValidationError(null);
                clearActivityStoreErrors();
              }}
            />
            <View style={styles.header}>
              <Text style={styles.headerText}>Today's Activity</Text>
              <Text style={styles.calorieText}>{totalBurnedCalories} kcal</Text>
            </View>
            <View>
              {isLoadingAllActivities ? (
                <SkeletonList items={3} height={80} gap={16} />
              ) : allActivities && allActivities.length > 0 ? (
                <FlatList
                  data={allActivities}
                  keyExtractor={(item) => item._id}
                  scrollEnabled={false}
                  contentContainerStyle={{ gap: 16 }}
                  renderItem={({ item }) => {
                    return (
                      <ActivityOverviewCard
                        activity={{
                          id: item._id,
                          title: item.activityType,
                          totalCalories: item.burnedCalories,
                          duration: item.durationInMinutes,
                          steps: item.steps
                        }}
                      />
                    );
                  }}
                />
              ) : (
                <View style={styles.emptyState}>
                  <Text style={styles.emptyStateText}>No activities recorded yet</Text>
                  <Text style={styles.emptyStateSubText}>Add your first activity below</Text>
                </View>
              )}
            </View>
            <Text style={[styles.headerText, { margin: 16, marginTop: 36 }]}>
              Add More
            </Text>
            <View style={{ gap: 36, marginTop: 8 }}>
              <CustomSelectWithLabel
                options={ACTIVITY_TYPES}
                label="Activity Type"
                selectedValue={selectedType}
                onValueChange={(value) => setSelectedType(value)}
                triggerZ={8}
                listZ={7}
                currentOpenDropdown={currentOpenDropdown}
                scrollToOffset={scrollToOffset}
                dropdownId={1}
                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
              />
              <CustomSelectWithLabel
                options={DURATIONS}
                label="Duration"
                selectedValue={selectedDuration}
                onValueChange={(value) => setSelectedDuration(value)}
                triggerZ={6}
                listZ={5}
                currentOpenDropdown={currentOpenDropdown}
                dropdownId={2}
                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                scrollToOffset={scrollToOffset}
              />
            </View>
            <CustomButton
              title="Save"
              onPress={handleSaveActivity}
              style={{
                alignSelf: "flex-end",
                marginTop: 24,
                width: 96,
              }}
            />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default EditActivitiesScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 8,
    paddingTop: 8,
  },
  content: {
    padding: 16,
    gap: 20,
  },
  header: {
    marginBottom: 24,
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 32,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.black,
    marginBottom: 8,
  },
  headerText: {
    fontSize: 32,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.black,
  },
  calorieText: {
    fontSize: 25,
    fontFamily: ThemeFonts.Exo_500,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    right: 0,
    padding: 16,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyStateText: {
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_600,
    color: Colors.black,
    textAlign: "center",
    marginBottom: 8,
  },
  emptyStateSubText: {
    fontSize: 14,
    fontFamily: ThemeFonts.Exo_400,
    color: Colors.gray,
    textAlign: "center",
  },
});
