import {
  <PERSON><PERSON><PERSON>,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useState, useEffect, useRef } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import {
  CustomAlert,
  CustomButton,
  CustomLoader,
} from "components/CustomAction";
import { useNavigation, useRoute } from "@react-navigation/native";
import { ACTIVITY_TYPES, DURATIONS } from "../constants";
import { activityService } from "services/activityService";
import ActivityOverviewCard from "../components/ActivityOverviewCard";

const reminders = [
  {
    id: 1,
    title: "Yoga",
    totalCalories: 400,
  },
  {
    id: 2,
    title: "Cycling",
    totalCalories: 400,
  },
  {
    id: 3,
    title: "Walking",
    totalCalories: 400,
  },
];

const EditActivitiesScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();

  const scrollViewRef = useRef();

  const [isLoading, setIsLoading] = useState(false);
  const [selectedType, setSelectedType] = useState(null);
  const [selectedDuration, setSelectedDuration] = useState(null);
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [editActivityError, setEditActivityError] = useState(null);

  useEffect(() => {
    if (route.params?.id) {
      // Add your fetch logic here for editing existing activity
    }
  }, [route.params?.id]);

  const handleSaveActivity = async () => {
    setIsLoading(true);

    const activityData = {
      type: selectedType,
      duration: selectedDuration,
    };

    const res = route.params?.id
      ? await activityService.updateActivity({
        id: route.params.id,
        ...activityData,
      })
      : await activityService.createActivity(activityData);

    setIsLoading(false);

    if (res.success) {
      navigation.goBack();
    } else {
      setEditActivityError(res.error);
    }
  };
  const scrollToOffset = (y) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: true });
  };

  if (isLoading) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <ScrollView showsVerticalScrollIndicator={false} ref={scrollViewRef}>
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View
            style={{ paddingHorizontal: 8, paddingTop: 8, paddingBottom: 200 }}
          >
            <CustomAlert
              visible={!!editActivityError}
              title={"Error"}
              message={editActivityError}
              buttons={[{ text: "OK", onPress: () => { } }]}
              onClose={() => setEditActivityError(null)}
            />
            <View style={styles.header}>
              <Text style={styles.headerText}>Today's Activity</Text>
              <Text style={styles.calorieText}>500 kcal</Text>
            </View>
            <View>
              <FlatList
                data={reminders}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
                contentContainerStyle={{ gap: 16 }}
                renderItem={({ item }) => {
                  return <ActivityOverviewCard activity={item} />;
                }}
              />
            </View>
            <Text style={[styles.headerText, { margin: 16, marginTop: 36 }]}>
              Add More
            </Text>
            <View style={{ gap: 36, marginTop: 8 }}>
              <CustomSelectWithLabel
                options={ACTIVITY_TYPES}
                label="Activity Type"
                selectedValue={selectedType}
                onValueChange={(value) => setSelectedType(value)}
                triggerZ={8}
                listZ={7}
                currentOpenDropdown={currentOpenDropdown}
                scrollToOffset={scrollToOffset}
                dropdownId={1}
                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
              />
              <CustomSelectWithLabel
                options={DURATIONS}
                label="Duration"
                selectedValue={selectedDuration}
                onValueChange={(value) => setSelectedDuration(value)}
                triggerZ={6}
                listZ={5}
                currentOpenDropdown={currentOpenDropdown}
                dropdownId={2}
                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                scrollToOffset={scrollToOffset}
              />
            </View>
            <CustomButton
              title="Save"
              onPress={handleSaveActivity}
              style={{
                alignSelf: "flex-end",
                marginTop: 24,
                width: 96,
              }}
            />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default EditActivitiesScreen;

const styles = StyleSheet.create({
  header: {
    marginBottom: 24,
    marginHorizontal: 16,
  },
  headerText: {
    fontSize: 32,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.black,
  },
  calorieText: {
    fontSize: 25,
    fontFamily: ThemeFonts.Exo_500,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    right: 0,
    padding: 16,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});
