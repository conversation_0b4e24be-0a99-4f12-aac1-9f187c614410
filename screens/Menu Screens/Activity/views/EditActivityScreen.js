import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableWithoutFeedback,
    View,
} from "react-native";
import React, { useEffect, useRef, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import {
    CustomAlert,
    CustomButton,
    CustomLoader,
} from "components/CustomAction";
import { useNavigation, useRoute } from "@react-navigation/native";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { ACTIVITY_TYPES, DURATIONS } from "../constants";
import { activityService } from "services/activityService";

const EditActivityScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const scrollViewRef = useRef();

    const [isLoading, setIsLoading] = useState(true);
    const [dataFetchError, setDataFetchError] = useState(null);

    const [initialData, setInitialData] = useState({
        type: "",
        duration: "",
    });

    const [selectedType, setSelectedType] = useState("yoga");
    const [selectedDuration, setSelectedDuration] = useState(45);
    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
    const [editActivityError, setEditActivityError] = useState(null);

    const [showDeleteConfirmPopup, setShowDeleteConfirmPopup] = useState(false);

    const [validationError, setValidationError] = useState(null);

    useEffect(() => {
        setIsLoading(true);
        fetchActivityData();
    }, [route.params.id]);

    const fetchActivityData = async () => {
        setIsLoading(false);
        // try {
        //     const res = await activityService.getActivity(route.params.id);
        //     if (res.success) {
        //         setSelectedType(res.data.type);
        //         setSelectedDuration(res.data.duration);
        //         setInitialData({
        //             type: res.data.type,
        //             duration: res.data.duration,
        //         });
        //     } else {
        //         setDataFetchError(res.error);
        //     }
        // } catch (error) {
        //     setDataFetchError("Failed to fetch activity data");
        // } finally {
        //     setIsLoading(false);
        // }
    };

    const validateData = () => {
        if (!selectedType) {
            setValidationError({
                typeError: "Please select an activity type",
            });
            return false;
        }

        if (!selectedDuration) {
            setValidationError({
                durationError: "Please select a duration",
            });
            return false;
        }

        setValidationError(null);
        return true;
    };

    const handleEditActivity = () => {
        if (
            initialData.type === selectedType &&
            initialData.duration === selectedDuration
        ) {
            navigation.goBack();
            return;
        }

        if (!validateData()) return;

        setIsLoading(true);
        (async () => {
            const res = await activityService.updateActivity({
                id: route.params.id,
                type: selectedType,
                duration: selectedDuration,
            });
            if (res.success) {
                navigation.goBack();
            } else {
                setEditActivityError(res.error);
            }
            setIsLoading(false);
        })();
    };

    const scrollToOffset = (y) => {
        scrollViewRef.current?.scrollTo({ y: y, animated: true });
    };

    const deleteActivity = async () => {
        setShowDeleteConfirmPopup(false);
        setIsLoading(true);
        const response = await activityService.deleteActivity(route?.params?.id);

        if (!response.success) {
            setEditActivityError(response.error);
        }

        setIsLoading(false);
        navigation.goBack();
    };

    if (isLoading) {
        return (
            <AppLayout>
                <View style={styles.loaderContainer}>
                    <CustomLoader />
                </View>
            </AppLayout>
        );
    }

    if (dataFetchError) {
        return (
            <AppLayout>
                <View
                    style={{
                        flex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                        padding: 16,
                        gap: 8,
                    }}
                >
                    <Text style={styles.dataFetchError}>{dataFetchError}</Text>
                    <CustomButton
                        title={"Go Back"}
                        onPress={() => {
                            navigation.goBack();
                        }}
                    />
                </View>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <TouchableWithoutFeedback
                onPress={() => {
                    setCurrentOpenDropdown(null);
                }}
            >
                <View style={{ flex: 1, flexDirection: "column", marginBottom: 80 }}>
                    <ScrollView
                        ref={scrollViewRef}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{}}
                    >
                        <TouchableWithoutFeedback
                            style={styles.container}
                            onPress={() => {
                                setCurrentOpenDropdown(null);
                            }}
                        >
                            <View style={{ paddingBottom: 120 }}>
                                <CustomAlert
                                    visible={!!editActivityError}
                                    title={"Error"}
                                    message={editActivityError}
                                    buttons={[
                                        {
                                            text: "OK",
                                            onPress: () => { },
                                        },
                                    ]}
                                    onClose={() => {
                                        setEditActivityError(null);
                                    }}
                                />
                                <View style={styles.header}>
                                    <Text style={styles.headerText}>
                                        Edit Activity
                                    </Text>
                                    <MaterialIcons name="edit" size={32} color={Colors.black} />
                                </View>
                                <View style={{ gap: 36, marginTop: 24 }}>
                                    <CustomSelectWithLabel
                                        options={ACTIVITY_TYPES}
                                        label="Activity Type"
                                        selectedValue={selectedType}
                                        onValueChange={(value) => setSelectedType(value)}
                                        triggerZ={8}
                                        listZ={7}
                                        currentOpenDropdown={currentOpenDropdown}
                                        scrollToOffset={(value) => scrollToOffset(value)}
                                        error={validationError?.typeError}
                                        dropdownId={1}
                                        setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                        clearValidationError={() => {
                                            setValidationError((prev) => ({
                                                ...prev,
                                                typeError: "",
                                            }));
                                        }}
                                    />
                                    <CustomSelectWithLabel
                                        options={DURATIONS}
                                        label="Duration"
                                        selectedValue={selectedDuration}
                                        onValueChange={(value) => setSelectedDuration(value)}
                                        triggerZ={6}
                                        listZ={5}
                                        currentOpenDropdown={currentOpenDropdown}
                                        scrollToOffset={(value) => scrollToOffset(value)}
                                        error={validationError?.durationError}
                                        dropdownId={2}
                                        setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                        clearValidationError={() => {
                                            setValidationError((prev) => ({
                                                ...prev,
                                                durationError: "",
                                            }));
                                        }}
                                    />
                                </View>
                            </View>
                        </TouchableWithoutFeedback>
                    </ScrollView>
                    <View style={styles.buttonContainer}>
                        <CustomButton
                            title={"Delete"}
                            onPress={() => {
                                setShowDeleteConfirmPopup(true);
                            }}
                            style={{
                                width: 96,
                                backgroundColor: Colors.white,
                                borderWidth: 2,
                                borderColor: Colors.primaryPurple,
                            }}
                            textColor={Colors.primaryPurple}
                        />
                        <CustomButton
                            title={"Save"}
                            onPress={handleEditActivity}
                            style={{
                                width: 96,
                            }}
                        />
                    </View>
                    <CustomAlert
                        title={"Delete Activity"}
                        message={"Do you want to delete this activity?"}
                        visible={showDeleteConfirmPopup}
                        buttons={[
                            {
                                text: "Cancel",
                                onPress: () => {
                                    setShowDeleteConfirmPopup(false);
                                },
                                style: "allowButton",
                            },
                            { text: "Delete", onPress: deleteActivity, style: "allowButton" },
                        ]}
                        onClose={() => {
                            setShowDeleteConfirmPopup(false);
                        }}
                    />
                </View>
            </TouchableWithoutFeedback>
        </AppLayout>
    );
};

export default EditActivityScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginBottom: 20,
        gap: 10,
        flexDirection: "column",
        justifyContent: "space-between",
        paddingBottom: 70,
        backgroundColor: Colors.lightOrange,
    },
    header: {
        flexDirection: "row",
        justifyContent: "flex-start",
        alignItems: "center",
        marginBottom: 20,
        marginHorizontal: 16,
        gap: 5,
    },
    headerText: {
        fontSize: 32,
        color: Colors.black,
        textAlign: "start",
        fontFamily: "Exo_700Bold",
        textTransform: "capitalize",
        textAlign: "left",
    },
    buttonContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 10,
        alignItems: "center",
        marginRight: 5,
    },
    dropdown: {
        marginVertical: 5,
    },
    loaderContainer: {
        flex: 1,
        bottom: 50,
        justifyContent: "center",
        alignItems: "stretch",
    },
    dataFetchError: {
        top: -20,
        fontSize: 16,
        fontFamily: "Exo_500Medium",
        textAlign: "center",
    },
});  
