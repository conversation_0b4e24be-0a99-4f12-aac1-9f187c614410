import React, { useState, useContext, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, Image, KeyboardAvoidingView, Platform, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Logo from 'assets/icon.png';  // Assuming this is your logo
import Image1 from 'assets/illustrator/illustrator_1.png';
import Image2 from 'assets/illustrator/illustrator_2.png';
import { AuthContext, useAuth } from 'context/AuthContext';
import { CustomButton, CustomInput } from 'components/CustomAction';
import { Colors } from 'constants/theme/colors';
import AsyncStorage from '@react-native-async-storage/async-storage'; // Import AsyncStorage
import authService from 'services/authService';
import CustomLoader from 'components/CustomAction/CustomLoader';
import CustomAlert from 'components/CustomAction/CustomAlert';
import { registerForPushNotificationsAsync } from 'services/pushNotificationRegistrationService';
import OpenSettingsPopup from 'components/PopUps/OpenSettingsPopup';
import { log } from 'react-native-reanimated';
import useNotificationStore from 'store/notificationStore';
import * as Device from 'expo-device';

export const LoginScreen = () => {
    const navigation = useNavigation();
    const { setNotificationStatus } = useNotificationStore(state => state);
    const { signIn } = useAuth();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);
    const [showAlert, setShowAlert] = useState(false);
    const [alertBox, setAlertBox] = useState({});

    const [showNotificationPermissionModel, setShowNotificationPermissionModel] = useState(false);

    const [statusCode, setStatusCode] = useState(null);

    const validatePassword = (password) => {
        const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        return regex.test(password);
    };

    const validateEmail = (email) => {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    };
    const validate = () => {
        const newErrors = {};
        if (!email) {
            newErrors.email = "Email is required";
        }
        else if (!validateEmail(email)) {
            newErrors.email = "Please enter a valid email";
        }
        if (!password) {
            newErrors.password = "Password is required";
        }
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleLogin = async () => {
        if (!validate()) return;
        try {
            setLoading(true);

            let pushTokenRes;

            // Check if running on a simulator
            if (!Device.isDevice) {
                // Use a static push token for simulator
                console.log("Using static push token for simulator");
                pushTokenRes = {
                    success: true,
                    pushTokenString: "SIMULATOR_STATIC_PUSH_TOKEN_12345"
                };
            } else {
                // Get actual push token on real device
                pushTokenRes = await registerForPushNotificationsAsync();

                console.log("login called");
                if (!pushTokenRes.success) {
                    setErrors((prevErrors) => ({
                        ...prevErrors,
                        login: pushTokenRes.error,
                    }));
                    setLoading(false);
                    return;
                }
            }

            const res = await signIn(email, password, pushTokenRes.pushTokenString);

            // const saveNotificationTokenRes = await setNotificationStatus(pushTokenRes.pushTokenString);

            // if (!saveNotificationTokenRes.success) {
            //     setErrors((prevErrors) => ({
            //         ...prevErrors,
            //         login: saveNotificationTokenRes.error,
            //     }));
            //     setLoading(false);
            //     return;
            // }

            if (!res.success) {
                setErrors((prevErrors) => ({
                    ...prevErrors,
                    login: res.error.message,
                }));

                const statusCode = res.error?.statusCode;
                setStatusCode(statusCode);

                if (statusCode === 401) {
                    setAlertBox({
                        title: "Invalid Credentials",
                        message: "Please check your email and password.",
                    });
                    setShowAlert(true);
                }
                if (statusCode === 404) {
                    setAlertBox({
                        title: "User Not Found",
                        message: "Please register before logging in.",
                    });
                    setShowAlert(true);
                }
                if (statusCode === 403) {
                    setAlertBox({
                        title: "Email Not Verified",
                        message: `A verification link was sent to ${email}. Please check your inbox.`,
                    });
                    setShowAlert(true);
                }

                // setShowAlert(true);
                setLoading(false);
                return;
            }

            setNotificationStatus({ isNotificationActive: res.isNotificationActive });

            // Return user data if login is successful and account is complete
            setShowAlert(false);
            setLoading(false);
            return res.user;

        } catch (error) {
            const errorMessage = error?.message || 'An unexpected error occurred';

            setAlertBox({
                title: "Error",
                message: errorMessage,
            });
            setShowAlert(true);

            setLoading(false);
        }
    };
    // Register
    // Handle Register button click
    const handleRegister = () => {
        navigation.navigate('Register');
    };

    const handleBlur = (field) => {
        // updateStepData("registration", { [field]: localValues[field] });
        setErrors((prevErrors) => {
            const newErrors = { ...prevErrors };
            if (field === "email" && validateEmail(email)) delete newErrors.email;
            if (field === "password" && validatePassword(password)) delete newErrors.password;
            return newErrors;
        });
    };

    const resendVerificationLink = async (email) => {
        try {
            await authService.resendVerificationLink(email);
            setAlertBox({
                title: "Email Sent",
                message: "Another verification email has been resent. Please check your inbox.",
            });
            setShowAlert(true);
        } catch (error) {
            setAlertBox({
                title: "Error",
                message: "Failed to send verification email. Please try again.",
            });
            setShowAlert(true);
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={styles.container}
            enabled={Platform.OS !== "ios"}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 80 : -38}
        >
            <CustomAlert
                visible={showAlert}
                title={alertBox.title}
                message={alertBox.message}

                buttons={
                    statusCode === 403
                        ? [
                            {
                                text: "Cancel",
                                onPress: () => setShowAlert(false),
                                style: "cancelButton"
                            },
                            {
                                text: "Send Again",
                                onPress: () => resendVerificationLink(email),
                                style: "allowButton"
                            }
                        ]
                        : [
                            {
                                text: "OK",
                                onPress: () => setShowAlert(false),
                                style: "allowButton"
                            }
                        ]
                }
                onClose={() => setShowAlert(false)}
            />
            {/* {showNotificationPermissionModel && <OpenSettingsPopup
                visible={showNotificationPermissionModel}
                title={"Notification Permission"}
                message={"Grant push notification to never miss an update."}
                onClose={() => setShowNotificationPermissionModel(false)}
            />} */}
            <TouchableWithoutFeedback accessible={false} onPress={Keyboard.dismiss}>
                <View style={{ flex: 1 }}>
                    {loading && <CustomLoader visible={loading} />}
                    <View style={styles.header}>
                        <Image source={Logo} style={styles.logo} />
                    </View>
                    <View style={styles.content}>
                        <Image source={Image1} style={styles.backgroundImage} />
                        <View>
                            <Text style={styles.title}>Log In</Text>
                            {errors.login && <Text style={styles.error}>{errors.login}</Text>}
                            <View style={styles.form}>
                                <CustomInput
                                    value={email}
                                    onChangeText={setEmail} // Update email state
                                    placeholder="Email"
                                    onBlur={() => handleBlur("email")}
                                    editable={!loading}
                                    clearValidationError={() => setErrors((prev) => ({ ...prev, email: "" }))}
                                />
                                {errors.email && <Text style={styles.error}>{errors.email}</Text>}

                                <CustomInput
                                    value={password}
                                    onChangeText={setPassword} // Update password state
                                    placeholder="Password"
                                    onBlur={() => handleBlur("password")}
                                    secureTextEntry
                                    editable={!loading}
                                    clearValidationError={() => setErrors((prev) => ({ ...prev, password: "" }))}
                                />
                                {errors.password && <Text style={styles.error}>{errors.password}</Text>}
                                <View style={styles.forgotPasswordContainer}>
                                    <Text
                                        style={styles.forgotPasswordText}
                                        onPress={() => navigation.navigate('Forgotpwd')}
                                    >
                                        Forgot Password?
                                    </Text>
                                </View>

                                <View style={styles.buttonContainer}>
                                    <CustomButton title="Register" onPress={handleRegister} />
                                    <CustomButton title="Login" onPress={handleLogin} />
                                </View>
                            </View>
                        </View>
                    </View>

                    <Image source={Image1} style={styles.backgroundImage2} />
                    <Image source={Image2} style={styles.backgroundImage3} />
                    <Image source={Image2} style={styles.backgroundImage4} />
                </View>
            </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
    );
};

// Stylesheet
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.primaryGreen,
        justifyContent: 'center',
    },
    header: {
        height: '25%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    logo: {
        width: 90,
        height: 85,
    },
    content: {
        flex: 1,
        width: '100%',
        backgroundColor: Colors.white,
        padding: 20,
        borderTopLeftRadius: 50,
        borderTopRightRadius: 50,
    },
    title: {
        fontSize: 32,
        fontFamily: 'Exo_700Bold',
        color: Colors.black,
        marginVertical: 20,
        textAlign: 'center',
    },
    form: {
        zIndex: 9998, // Increased zIndex to stack above images
    },
    backgroundImage: {
        position: 'absolute',
        top: '12%',
        right: '-8%',
        width: '25%',
        height: '25%',
        resizeMode: 'contain',
        zIndex: 1,
        opacity: 0.7,
        transform: [{ translateY: '-50%' }],
    },
    backgroundImage2: {
        position: 'absolute',
        bottom: '-35%',
        left: '-10%',
        width: '45%',
        height: '45%',
        resizeMode: 'contain',
        zIndex: 1,
        opacity: 0.7,
        transform: [{ translateY: '-50%' }],
    },
    backgroundImage3: {
        position: 'absolute',
        top: '40%',
        left: '-15%',
        width: '45%',
        height: '45%',
        resizeMode: 'contain',
        zIndex: 1,
        opacity: 0.9,
        transform: [{ translateY: '-50%' }, { rotate: '0deg' }],
    },
    backgroundImage4: {
        position: 'absolute',
        top: '96%',
        right: '-15%',
        width: '45%',
        height: '45%',
        resizeMode: 'contain',
        zIndex: 1,
        opacity: 0.9,
        transform: [{ translateY: '-50%' }, { rotate: '50deg' }],
    },
    error: {
        color: 'red',
        fontSize: 14,
        width: '100%',
        fontFamily: 'Exo_400Regular',
        textAlign: 'left',
        marginLeft: 23,
        // marginHorizontal: 'auto',
        zIndex: 9998
    },
    buttonContainer: {
        borderRadius: 50,
        alignItems: 'center',
        marginTop: 20,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    forgotPasswordContainer: {
        alignItems: 'flex-end',
        marginBottom: 20,
    },
    forgotPasswordText: {
        color: 'black',
        textDecorationLine: 'underline',
        fontFamily: 'Exo_400Regular',
    },
});