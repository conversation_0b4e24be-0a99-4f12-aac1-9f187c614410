import React, { useState } from "react";
import { View, Text, StyleSheet, KeyboardAvoidingView, Platform, ScrollView, Keyboard } from "react-native";
import { CustomButton, CustomInput } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import { useNavigation } from '@react-navigation/native';
import authService from 'services/authService';
import CustomLoader from 'components/CustomAction/CustomLoader';
import CustomAlert from 'components/CustomAction/CustomAlert';


export const ForgotPassword = () => {
	const navigator = useNavigation();
	const [email, setEmail] = useState("");
	const [error, setError] = useState("");
	const [showAlert, setShowAlert] = useState(false);
	const [loading, setLoading] = useState(false);

	const validateEmail = (email) => {
		const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return regex.test(email);
	};

	const handleForgotPassword = async () => {
		setLoading(true);
		setError("");
		if (!email) {
			setError("Email is required");
			setLoading(false);
			return;
		}
		if (!validateEmail(email)) {
			setError("Please enter a valid email");
			setLoading(false);
			return;
		}
		const res = await authService.forgotPassword(email);
		if (res.success) setShowAlert(true);
		else setError(res.message);
		setLoading(false);
	};

	return (
		<KeyboardAvoidingView
			style={styles.container}
			behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
			keyboardVerticalOffset={Platform.OS === 'ios' ? 80 : -38}
		>
			<CustomAlert
				visible={showAlert}
				title="Check Your Email"
				message={`A password reset link has been sent to ${email}. Please check your inbox.`}
				buttons={[{ text: "OK", onPress: () => navigator.navigate('Login'), style: "allowButton" }]}
				onClose={() => setShowAlert(false)}
			/>
			<ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>
				{loading && <CustomLoader />}
				<Text style={styles.text}>Forgot Password</Text>
				<CustomInput
					value={email}
					onChangeText={setEmail}
					placeholder="Enter your email"
					onBlur={() => setError(validateEmail(email) ? "" : "Please enter a valid email")}
					editable={true}
				/>
				{error && <Text style={styles.error}>{error}</Text>}
				<View style={styles.buttonContainer}>
					<CustomButton
						title="Back"
						onPress={() => navigator.navigate("login")}
						textColor={Colors.primaryPurple}
						backgroundColor={Colors.lightGreen}
						textStyle={{ fontFamily: "Exo_700Bold" }}
					/>
					<CustomButton
						title="Submit"
						onPress={handleForgotPassword}
						textColor={Colors.primaryPurple}
						backgroundColor={Colors.lightGreen}
						textStyle={{ fontFamily: "Exo_700Bold" }}
					/>
				</View>
			</ScrollView>
		</KeyboardAvoidingView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		padding: 20,
		backgroundColor: Colors.primaryGreen,
	},
	scrollContainer: {
		marginVertical: 200,
		paddingBottom: 0,
	},
	error: {
		color: 'red',
		fontSize: 12,
		marginHorizontal: 10,
		fontFamily: 'Exo_400Regular',
	},
	buttonContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		marginTop: 20,
	},
	text: {
		fontSize: 32,
		marginBottom: 38,
		fontFamily: 'Exo_700Bold',
		textAlign: 'center',
		color: Colors.white,
	},
});
