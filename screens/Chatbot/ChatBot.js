import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    FlatList,
    KeyboardAvoidingView,
    Platform,
    ActivityIndicator,
    Keyboard,
    Linking
} from 'react-native';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import Icon from 'react-native-vector-icons/Ionicons';
import * as DocumentPicker from 'expo-document-picker';
import { CustomAlert } from 'components/CustomAction';
import useChatbotStore from 'store/chatbotStore';
import MessageBubble from './components/MessageBubble';
import AppLayout from 'navigations/components/Layouts/AppLayout';

const MAX_ATTACHMENTS = 3;

const ChatBot = () => {
    // State
    const [message, setMessage] = useState('');
    const [attachments, setAttachments] = useState([]);
    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');
    const [showOptions, setShowOptions] = useState(true);

    // Refs
    const flatListRef = useRef(null);

    // Store
    const {
        messages,
        isLoading,
        predefinedOptions,
        initializeChatbot,
        sendMessage,
        sendPredefinedOption
    } = useChatbotStore();

    // Initialize chatbot
    useEffect(() => {
        initializeChatbot();
    }, []);

    // Scroll to bottom when new messages arrive
    useEffect(() => {
        if (messages.length > 0 && flatListRef.current) {
            setTimeout(() => {
                flatListRef.current.scrollToEnd({ animated: true });
            }, 200);
        }
    }, [messages]);

    // Render messages with predefined options inside welcome message
    const renderMessageItem = ({ item }) => {
        // If it's the welcome message, add options to it
        if (item.id === 'welcome' && showOptions && predefinedOptions.length > 0) {
            // Create a new message object with options
            const messageWithOptions = {
                ...item,
                options: predefinedOptions,
                onOptionPress: handleOptionPress
            };

            return (
                <MessageBubble
                    message={messageWithOptions}
                    onAttachmentPress={handleAttachmentPress}
                />
            );
        }

        // Regular message
        return (
            <MessageBubble
                message={item}
                onAttachmentPress={handleAttachmentPress}
            />
        );
    };

    // Handle attachment
    const handleAttachment = async () => {
        try {
            // Don't allow more than MAX_ATTACHMENTS
            if (attachments.length >= MAX_ATTACHMENTS) {
                setAlertMessage(`You can attach up to ${MAX_ATTACHMENTS} files only.`);
                setShowAlert(true);
                return;
            }

            const result = await DocumentPicker.getDocumentAsync({
                type: ['image/*', 'application/pdf'],
                copyToCacheDirectory: true,
                multiple: true,
            });

            // Handle result
            if (!result.canceled) {
                // Some versions may return `assets` as a single object instead of an array
                const newFiles = Array.isArray(result.assets)
                    ? result.assets
                    : result.assets
                        ? [result.assets]
                        : [];

                // Check if adding these files would exceed the limit
                if (attachments.length + newFiles.length > MAX_ATTACHMENTS) {
                    setAlertMessage(`You can attach up to ${MAX_ATTACHMENTS} files only.`);
                    setShowAlert(true);

                    // Add only enough files to reach the limit
                    const availableSlots = MAX_ATTACHMENTS - attachments.length;
                    if (availableSlots > 0) {
                        setAttachments([
                            ...attachments,
                            ...newFiles.slice(0, availableSlots),
                        ]);
                    }
                } else {
                    setAttachments([
                        ...attachments,
                        ...newFiles,
                    ]);
                }
            }
        } catch (error) {
            console.error('Error picking document:', error);
            setAlertMessage('Error selecting file. Please try again.');
            setShowAlert(true);
        }
    };

    // Handle delete attachment
    const handleDeleteAttachment = (uri) => {
        setAttachments(attachments.filter(file => file.uri !== uri));
    };

    // Handle send message
    const handleSendMessage = async () => {
        if ((!message.trim() && attachments.length === 0) || isLoading) return;

        await sendMessage(message.trim(), attachments);
        setMessage('');
        setAttachments([]);
        setShowOptions(false);
        Keyboard.dismiss();
    };

    // Handle option press
    const handleOptionPress = async (option) => {
        await sendPredefinedOption(option);
        setShowOptions(false);
    };

    // Handle attachment press
    const handleAttachmentPress = (file) => {
        Linking.openURL(file.uri);
    };

    // Render attachments
    const renderAttachments = () => {
        if (attachments.length === 0) return null;

        return (
            <View style={styles.attachmentsContainer}>
                {attachments.map((file) => (
                    <View key={file.uri} style={styles.attachedFileContainer}>
                        <View style={styles.attachedFileContent}>
                            <Icon
                                name={
                                    file.mimeType?.startsWith('image/')
                                        ? 'image-outline'
                                        : 'document-text-outline'
                                }
                                size={16}
                                color={Colors.primaryPurple}
                            />
                            <View style={styles.fileInfoContainer}>
                                <Text
                                    style={styles.attachedFileName}
                                    numberOfLines={1}
                                    ellipsizeMode="tail"
                                >
                                    {file.name}
                                </Text>
                            </View>
                            <TouchableOpacity onPress={() => handleDeleteAttachment(file.uri)}>
                                <Icon name="trash-outline" size={16} color={Colors.primaryPurple} />
                            </TouchableOpacity>
                        </View>
                    </View>
                ))}
            </View>
        );
    };

    return (
        <AppLayout illustration={false}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={styles.container}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
            >
                <View style={styles.innerContainer}>
                    {/* Header */}
                    <View style={styles.headerContainer}>
                        <Text style={styles.headerText}>Chatbot</Text>
                        <Text style={styles.subHeaderText}>Your virtual health assistant</Text>
                    </View>

                    {/* Messages - Scrollable */}
                    <View style={styles.messagesContainer}>
                        <FlatList
                            ref={flatListRef}
                            data={messages}
                            keyExtractor={(item) => item.id}
                            renderItem={renderMessageItem}
                            contentContainerStyle={styles.messagesContent}
                            showsVerticalScrollIndicator={true}
                            onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
                            onLayout={() => flatListRef.current?.scrollToEnd({ animated: true })}
                            scrollEnabled={true}
                        />
                    </View>

                    {/* Input Area - Fixed at bottom */}
                    <View style={styles.inputContainer}>
                        {renderAttachments()}

                        <View style={styles.inputRow}>
                            <View style={styles.inputWrapper}>


                                <TextInput
                                    style={styles.input}
                                    value={message}
                                    onChangeText={setMessage}
                                    placeholder="Type your message..."
                                    placeholderTextColor={Colors.darkGray}
                                    multiline
                                />
                                <TouchableOpacity
                                    style={styles.attachButton}
                                    onPress={handleAttachment}
                                >
                                    <Icon name="attach" size={24} color={Colors.primaryPurple} />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={[
                                        styles.sendButton,
                                        (!message.trim() && attachments.length === 0) && styles.sendButtonDisabled
                                    ]}
                                    onPress={handleSendMessage}
                                    disabled={(!message.trim() && attachments.length === 0) || isLoading}
                                >
                                    {isLoading ? (
                                        <ActivityIndicator color={Colors.white} size="small" />
                                    ) : (
                                        <Icon name="send" size={20} color={Colors.white} />
                                    )}
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    {/* Alert */}
                    <CustomAlert
                        visible={showAlert}
                        title="Attachment Limit"
                        message={alertMessage}
                        buttons={[
                            {
                                text: "OK",
                                onPress: () => setShowAlert(false),
                                style: "allowButton",
                            },
                        ]}
                        onClose={() => setShowAlert(false)}
                    />
                </View>
            </KeyboardAvoidingView>
        </AppLayout>
    );
};

export default ChatBot;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingBottom: 64,
        // padding: 8,
    },
    innerContainer: {
        flex: 1,
        backgroundColor: Colors.white,
        display: 'flex',
        flexDirection: 'column',
    },
    headerContainer: {
        paddingTop: 10,
        paddingBottom: 5,
    },
    headerText: {
        fontSize: 32,
        color: Colors.black,
        textAlign: 'start',
        marginHorizontal: 24,
        fontFamily: ThemeFonts.Exo_700,
    },
    subHeaderText: {
        fontSize: 16,
        color: Colors.black,
        textAlign: 'start',
        marginHorizontal: 24,
        marginBottom: 4,
        fontFamily: ThemeFonts.Exo_500,
    },
    messagesContainer: {
        flex: 1,
        width: '100%',
    },
    messagesContent: {
        paddingVertical: 8,
        paddingHorizontal: 4,
        flexGrow: 1,
    },
    inputContainer: {
        borderTopWidth: 1,
        borderTopColor: Colors.lightGray,
        paddingVertical: 12,
        paddingHorizontal: 12, // Increased horizontal padding for better spacing
        backgroundColor: Colors.white,
        width: '100%',
        bottom: 0,
    },
    inputRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    inputWrapper: {
        flex: 1,
        flexDirection: 'row',
        position: 'relative',
        alignItems: 'center',
        backgroundColor: Colors.lightGray,
        borderRadius: 20,
        paddingVertical: 5,
    },
    input: {
        flex: 1,
        paddingHorizontal: 8,
        paddingLeft: 10,
        paddingRight: 10,
        paddingVertical: 8,
        maxHeight: 100,
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.black,
    },
    attachButton: {
        padding: 8,
        marginLeft: 5,
        justifyContent: 'center',
        alignItems: 'center',
    },
    sendButton: {
        backgroundColor: Colors.primaryGreen,
        borderRadius: 20,
        width: 36,
        height: 36,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 5,
    },
    sendButtonDisabled: {
        backgroundColor: Colors.gray,
    },
    attachmentsContainer: {
        marginBottom: 8,
    },
    attachedFileContainer: {
        width: '100%',
        marginBottom: 5,
    },
    attachedFileContent: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 4,
        paddingVertical: 6,
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
        borderRadius: 10,
        backgroundColor: Colors.lightGray,
    },
    fileInfoContainer: {
        flex: 1,
        marginHorizontal: 10,
    },
    attachedFileName: {
        fontSize: 12,
        fontFamily: ThemeFonts.Exo_600,
        color: Colors.black,
        marginLeft: -1,
    },
});