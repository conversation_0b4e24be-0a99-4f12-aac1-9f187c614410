import React, { useEffect, useState } from "react";
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Image,
    ScrollView,
    TouchableWithoutFeedback,
} from "react-native";
import { CustomAlert, CustomButton, CustomLoader, CustomSelect } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import Circle from "assets/illustrator/illustrator_3.png";
import Lemon_2 from "assets/illustrator/illustrator_4.png";
import { devices as fallbackDevices } from "constants/options";
import deviceService from "services/deviceService";
import { screenWidth } from "constants/sizes";
import useOnboardingFormStore from "store/onboadingFormStore";
import { ThemeFonts } from "constants/theme/fonts";
import userService from "services/userService";
import { useNavigation } from "@react-navigation/native";
import { useAuth } from "context/AuthContext";

export const SelectDeviceScreen2 = () => {

    const navigation = useNavigation();

    const { currentOnboardingStep, profile, goals, deviceDetails, app_permissions } = useOnboardingFormStore(state => state);
    const { setDeviceOnboardingDetails, setOnboardingStep, resetOnboardingForm } =
        useOnboardingFormStore((state) => state);

    const { updateUserProfileLocal } = useAuth();

    const [devices, setDevices] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedDevice, setSelectedDevice] = useState({
        id: deviceDetails?.id || "",
        name: deviceDetails?.name || "",
        version: deviceDetails?.version || "",
        image: deviceDetails?.image || "",
        type: deviceDetails?.type || "",
        referenceId: deviceDetails?.referenceId || "",
    });

    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    const formatDeviceData = (device) => ({
        ...device,
        name:
            device.name.charAt(0).toUpperCase() + device.name.slice(1).toLowerCase(),
        version: `V${device.version}`,
        image: device.thumbnailUrl,
    });

    useEffect(() => {
        const fetchDevices = async () => {
            try {
                setLoading(true);
                const res = await deviceService.getDevices();

                if (res.success && res.data.length > 0) {
                    const formattedDevices = res.data.map(formatDeviceData);
                    setDevices(formattedDevices);
                } else {
                    const formattedFallbackDevices =
                        fallbackDevices.map(formatDeviceData);
                    setDevices(formattedFallbackDevices);
                }
            } catch (err) {
                setError("Failed to fetch devices");
                const formattedFallbackDevices = fallbackDevices.map(formatDeviceData);
                setDevices(formattedFallbackDevices);
            } finally {
                setLoading(false);
            }
        };

        fetchDevices();
    }, []);

    const handleDeviceSelect = (index) => {
        setSelectedDevice(index);
    };

    const handleNext = () => {
        if (currentOnboardingStep < 5) {
            setOnboardingStep(currentOnboardingStep + 1);
            setDeviceOnboardingDetails({
                ...selectedDevice,
            });
        }
    };

    const handleBack = () => {
        if (currentOnboardingStep > 1) {
            setOnboardingStep(currentOnboardingStep - 1);
        }
    };


    const handleSkip = async () => {
        setIsLoading(true);
        setError('');

        try {
            const userProfileData = {
                age: Number(profile.age) || 0,
                gender: profile.sex,
                height: Number(profile.height) || 0,
                weight: Number(profile.weight) || 0,
                diet_preference: profile.dietPreference,
                physical_goal: goals.physical,
                activity_goal: goals.movement,
                mind_goal: goals.mindfulness,
                sleep_goal: goals.sleep,
                deviceUsageLimit: goals.deviceUsageLimit == "null" ? null : goals.deviceUsageLimit,
                app_permissions: [...app_permissions]
            };

            const res = await userService.completeUserProfile(userProfileData);

            if (res.success) {
                await updateUserProfileLocal({
                    ...res.data.user,
                    isAccountCompleted: true
                });

                await resetOnboardingForm();
                // navigation.navigate('dashboard');
            } else {
                setError('Device ID validation failed');
            }
        } catch (err) {
            setError('An error occurred while validating the device');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
            <View style={styles.container}>
                <View style={styles.backgroundImage} pointerEvents="none">
                    <Image
                        source={Circle}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="contain"
                    />
                </View>
                <View style={styles.content}>
                    <Text style={styles.title}>Select a Device</Text>
                    <Text style={styles.desc}>
                        Pair your device for customised recommendations.
                    </Text>
                    {isLoading ? (
                        <View style={[styles.centerContent, { marginBottom: 30 }]}>
                            <CustomLoader />
                        </View>
                    ) : (
                        <ScrollView
                            contentContainerStyle={{ flex: 1 }}
                            showsVerticalScrollIndicator={false}
                        >
                            <View style={styles.devicesContainer}>
                                <View style={{ borderRadius: 25, overflow: "visible" }}>
                                    <Image source={{ uri: 'https://cdn11.bigcommerce.com/s-ilgxsy4t82/images/stencil/1280x1280/products/323073/1136161/61--uDS1DJL._AC_SL1500___09686.1739336657.jpg?c=1&imbypass=on' }} style={styles.deviceBGImage} />
                                    <View style={{ position: "absolute", left: 8, right: 8, bottom: 8 }}>
                                        <CustomSelect options={[
                                            ...devices.map((device, index) => ({
                                                label: device?.name,
                                                value: device?.id,
                                            })),
                                        ]} selectedValue={selectedDevice} onValueChange={handleDeviceSelect} setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)} currentOpenDropdown={currentOpenDropdown} dropdownId={"device_dropdown"} triggerZ={3000} listZ={1000} label="Select Device"
                                            loading={loading}
                                        />
                                    </View>
                                </View>
                            </View>
                        </ScrollView>
                    )}
                </View>
                <View style={styles.lemon} pointerEvents="none">
                    <Image
                        source={Lemon_2}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="contain"
                    />
                </View>
                <View style={styles.footer}>
                    <View style={styles.buttonContainer}>
                        <CustomButton title="Back" onPress={handleBack} />
                        <CustomButton title="Skip >" onPress={handleSkip} style={{
                            backgroundColor: Colors.white,
                            borderColor: Colors.white,
                        }} textColor={Colors.primaryPurple} />
                        <CustomButton title="Next" onPress={handleNext} />
                    </View>
                </View>
                <CustomAlert
                    visible={!!error}
                    title="Error"
                    message={error}
                    buttons={[
                        { text: "OK", onPress: () => setError(null), style: "allowButton" },
                    ]}
                    onClose={() => setError(null)}
                />
            </View>
        </TouchableWithoutFeedback>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.primaryGreen,
    },
    content: {
        flex: 1,
        padding: 20,
        paddingBottom: 0,
        justifyContent: "center",
        marginTop: 50,
        zIndex: 9999,
    },
    title: {
        fontSize: 35,
        fontFamily: "Exo_700Bold",
        color: Colors.white,
        marginBottom: 10,
        textAlign: "center",
    },
    desc: {
        fontSize: 18,
        fontFamily: "Exo_400Regular",
        color: Colors.white,
        marginBottom: 30,
        textAlign: "center",
    },
    devicesContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
    centerSingleDevice: {
        justifyContent: "center",
        alignItems: "center",
    },
    rowTwoDevices: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
    },
    rowMultipleDevices: {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "center",
        alignItems: "center",
    },
    deviceBox: {
        backgroundColor: Colors.white,
        padding: 20,
        borderRadius: 15,
        justifyContent: "center",
        width: 100,
        height: 100,
        borderColor: Colors.darkGreen,
        borderRightWidth: 7,
        borderBottomWidth: 7,
    },
    selectedDevice: {
        backgroundColor: Colors.white,
        borderColor: Colors.primaryPurple,
        borderWidth: 2,
        transform: [{ scale: 1.0 }],
        // Add subtle shadow for selected state
        shadowColor: Colors.primaryPurple,
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 4.65,
        elevation: 8,
    },
    centerContent: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
    loadingText: {
        color: Colors.white,
        fontSize: 18,
        fontFamily: "Exo_400Regular",
    },
    errorText: {
        color: Colors.white,
        fontSize: 16,
        fontFamily: "Exo_400Regular",
        textAlign: "center",
    },
    deviceHeading: {
        color: Colors.black,
        fontSize: 18,
        fontFamily: "Exo_700Bold",
        textAlign: "center",
    },
    deviceText: {
        color: Colors.black,
        fontSize: 12,
        fontFamily: "Exo_400Regular",
        textAlign: "center",
    },
    footer: {
        height: "25%",
        backgroundColor: Colors.white,
        borderTopLeftRadius: 50,
        borderTopRightRadius: 50,
        justifyContent: "center",
        padding: 20,
    },
    buttonContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        zIndex: 99,
    },
    lemon: {
        position: "absolute",
        top: "71%",
        left: "-17%",
        width: "55%",
        height: "55%",
        resizeMode: "contain",
        zIndex: 99, // Lower than the content and form containers
        transform: [{ translateY: "-50%" }, { rotate: "10deg" }],
    },
    backgroundImage: {
        position: "absolute",
        top: "8%",
        right: "-10%",
        width: "50%",
        height: "50%",
        resizeMode: "contain",
        zIndex: 99, // Lower than the content and form containers
        transform: [{ translateY: "-50%" }, { rotate: "-5deg" }],
    },
    deviceBGImage: {
        width: "100%",
        aspectRatio: 16 / 10.5,
        resizeMode: "cover",
        borderRadius: 25,
    }
});
