import React, { useState, useEffect } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { CustomButton, CustomInput } from 'components/CustomAction';
import { Colors } from 'constants/theme/colors';
import { useAuth } from 'context/AuthContext';
import userService from 'services/userService';
import CustomLoader from 'components/CustomAction/CustomLoader';
import CustomAlert from 'components/CustomAction/CustomAlert';

// Import images
import Circle from 'assets/illustrator/illustrator_3.png';
import Leaf from 'assets/illustrator/illustrator_2.png';
import Lemon_2 from 'assets/illustrator/illustrator_1.png';
import Lemon_1 from 'assets/illustrator/illustrator_5.png';
import OpenSettingsPopup from 'components/PopUps/OpenSettingsPopup';
import useOnboardingFormStore from 'store/onboadingFormStore';

export const PairDeviceScreen = ({ navigation }) => {

    const { currentOnboardingStep, profile, goals, deviceDetails, app_permissions } = useOnboardingFormStore(state => state);
    const { setOnboardingStep, resetOnboardingForm } = useOnboardingFormStore(state => state);

    const [referenceId, setReferenceId] = useState('');

    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [showAlert, setShowAlert] = useState(false);
    const { updateUserProfileLocal } = useAuth();

    const [showNotificationPermissionModel, setShowNotificationPermissionModel] = useState(false);

    const handleBack = () => {
        if (currentOnboardingStep > 1) {
            setOnboardingStep(currentOnboardingStep - 1);
        }
    };

    const handleAdd = async () => {
        setIsLoading(true);
        setError('');

        try {
            const userProfileData = {
                age: Number(profile.age) || 0,
                gender: profile.sex,
                height: Number(profile.height) || 0,
                weight: Number(profile.weight) || 0,
                diet_preference: profile.dietPreference,
                physical_goal: goals.physical,
                activity_goal: goals.movement,
                mind_goal: goals.mindfulness,
                sleep_goal: goals.sleep,
                deviceUsageLimit: goals.deviceUsageLimit == "null" ? null : goals.deviceUsageLimit,
                deviceData: {
                    serialId: deviceDetails?.referenceId,
                    deviceId: deviceDetails.id,
                },
                app_permissions: [...app_permissions]
            };

            const res = await userService.completeUserProfile(userProfileData);

            // await registerForPushNotificationsAsync();
            // console.log('res===', res.data.device);
            if (res.success) {
                await updateUserProfileLocal({
                    ...res.data.user,
                    isAccountCompleted: true
                });

                await resetOnboardingForm();

                navigation.navigate('Dashboard');
            } else {
                setError('Device ID validation failed');
            }
        } catch (err) {
            setError('An error occurred while validating the device');
        } finally {
            setIsLoading(false);
        }
    };

    const handleSkip = async () => {
        setIsLoading(true);
        setError('');

        try {

            const userProfileData = {
                age: Number(profile.age) || 0,
                gender: profile.sex,
                height: Number(profile.height) || 0,
                weight: Number(profile.weight) || 0,
                diet_preference: profile.dietPreference,
                physical_goal: goals.physical,
                activity_goal: goals.movement,
                mind_goal: goals.mindfulness,
                sleep_goal: goals.sleep,
                deviceUsageLimit: goals.deviceUsageLimit == "null" ? null : goals.deviceUsageLimit,
                app_permissions: [...app_permissions]
            };

            const res = await userService.completeUserProfile(userProfileData);

            if (res.success) {
                await updateUserProfileLocal({
                    ...res.data.user,
                    isAccountCompleted: true
                });
                await resetOnboardingForm();
                setShowAlert(true);
            } else {
                setError(res.error || 'Profile update failed');
            }
        } catch (err) {
            setError('An error occurred while updating profile');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <View style={styles.container}>
            <Image source={Lemon_2} style={styles.lemon} />
            <Image source={Lemon_1} style={styles.lemon_2} />
            <Image source={Leaf} style={styles.leaf_2} />

            <View style={styles.content}>
                <Text style={styles.title}>Pair Your Device</Text>

                {isLoading ? (
                    <CustomLoader />
                ) : (
                    <>
                        <View style={styles.referenceImage}>
                            {deviceDetails?.image ? (
                                <Image
                                    source={deviceDetails?.image}
                                    style={styles.deviceImageStyle}
                                    resizeMode="contain"
                                />
                            ) : (
                                <Text style={{ textAlign: 'start', fontFamily: 'Exo_400Regular' }}>Reference Image</Text>
                            )}
                        </View>
                        {showNotificationPermissionModel && <OpenSettingsPopup
                            visible={showNotificationPermissionModel}
                            title={"Notification Permission"}
                            message={"Grant push notification to never miss an update."}
                            onClose={() => setShowNotificationPermissionModel(false)}
                        />}
                        <CustomInput
                            placeholder="ID Number"
                            value={referenceId}
                            onChangeText={setReferenceId}
                            editable={true}
                        />
                        {error && <Text style={styles.error}>{error}</Text>}
                    </>
                )}

                <View style={styles.footer}>
                    <View style={styles.buttonContainer}>
                        <CustomButton
                            title="Back"
                            onPress={handleBack}
                            isBack
                        />
                        <CustomButton
                            title="Add"
                            onPress={handleAdd}
                            disabled={isLoading}
                            disabledBgColor={Colors.primaryPurple}
                            disabledTextColor={Colors.white}
                        />
                    </View>

                    <TouchableOpacity style={{ textAlign: 'end', left: 92 }} onPress={handleSkip}>
                        <Text style={styles.skipText}>{"Skip >"}</Text>
                    </TouchableOpacity>
                </View>
            </View>

            <Image source={Leaf} style={styles.leaf} />

            <CustomAlert
                visible={showAlert}
                title="Device Pairing Skipped"
                message="You can pair your device later in the settings."
                buttons={[
                    {
                        text: "OK",
                        onPress: () => {
                            setShowAlert(false);
                            navigation.navigate('Dashboard');
                        },
                        style: "allowButton"
                    }
                ]}
                onClose={() => setShowAlert(false)}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: Colors.white,
    },
    content: {
        flex: 1,
        marginHorizontal: 20,
        justifyContent: 'center',
        alignItems: 'center',
        width: '90%',
        zIndex: 9999,
    },
    title: {
        fontSize: 24,
        fontFamily: 'Exo_500Medium',
        marginBottom: 20,
    },
    referenceImage: {
        backgroundColor: Colors.white,
        width: "100%",
        height: 160,
        padding: 20,
        marginBottom: 10,
        borderRadius: 20,
        borderColor: Colors.primaryPurple,
        borderWidth: 2,
        zIndex: 9999,
        justifyContent: 'center',
        alignItems: 'center',
    },
    deviceImageStyle: {
        width: '100%',
        height: '100%',
        zIndex: 21,
    },
    error: {
        color: 'red',
        marginBottom: 20,
    },
    footer: {
        alignItems: 'center',
        width: '100%',
        marginTop: 20,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: 20,
    },
    skipText: {
        color: Colors.primaryPurple,
        textDecorationLine: 'underline',
        fontFamily: 'Exo_400Regular',
    },
    lemon: {
        position: 'absolute',
        bottom: '-51%',
        left: '-15%',
        width: '55%',
        height: '55%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '0deg' }],
    },
    lemon_2: {
        position: 'absolute',
        top: '3%',
        right: '-4%',
        width: '25%',
        height: '25%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '-26deg' }],
    },
    leaf: {
        position: 'absolute',
        bottom: '-43%',
        right: '-15%',
        width: '50%',
        height: '50%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '45deg' }],
    },
    leaf_2: {
        position: 'absolute',
        top: '27%',
        left: '-12%',
        width: '50%',
        height: '50%',
        resizeMode: 'contain',
        zIndex: 9997,
        transform: [{ translateY: '-50%' }, { rotate: '10deg' }],
    }
});

export default PairDeviceScreen;