import { CustomButton, CustomInput, CustomSelect, DateOfBirthPicker } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import React, { useState, useEffect } from "react";
import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableWithoutFeedback,
    ScrollView,
    Keyboard,
    Platform
} from "react-native";
import Lemon from 'assets/illustrator/illustrator_1.png';
import Leaf from 'assets/illustrator/Illustrator_6.png';
import CustomAlert from 'components/CustomAction/CustomAlert';
import { useAuth } from 'context/AuthContext';
import { dietOptions } from 'constants/options';
import useOnboardingFormStore from "store/onboadingFormStore";
import { KeyboardProvider, KeyboardAvoidingView, KeyboardAwareScrollView } from 'react-native-keyboard-controller';

export const ProfileSetupScreen = () => {
    const { state: session } = useAuth();

    const { currentOnboardingStep, profile } = useOnboardingFormStore(state => state);
    const { setProfileOnboardingData, setOnboardingStep } = useOnboardingFormStore(state => state);

    const [showPermissionAlert, setShowPermissionAlert] = useState(false);
    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
    const [keyboardVisible, setKeyboardVisible] = useState(false);

    const [userProfileData, setUserProfileData] = useState({
        age: profile?.age || "",
        dob: profile?.dob || "",
        sex: profile?.sex || "",
        height: profile?.height || "",
        weight: profile?.weight || "",
        dietPreference: profile?.dietPreference || "",
    });

    const [localErrors, setLocalErrors] = useState({});

    // Add keyboard listeners
    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener(
            Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
            () => setKeyboardVisible(true)
        );
        const keyboardDidHideListener = Keyboard.addListener(
            Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
            () => setKeyboardVisible(false)
        );

        // Clean up listeners
        return () => {
            keyboardDidShowListener.remove();
            keyboardDidHideListener.remove();
        };
    }, []);

    const validate = () => {
        const newErrors = {};

        if (!userProfileData?.dob) newErrors.dob = "Please select your date of birth.";
        else if (!userProfileData?.age || Number(userProfileData?.age) < 3 || Number(userProfileData?.age) > 120) newErrors.dob = "Age must be in between 3-120 years.";
        else if (!userProfileData?.sex) newErrors.sex = "Please select your sex.";
        else if (!userProfileData?.height) newErrors.height = "Please enter your height.";
        else if (Number(userProfileData?.height) < 50 || Number(userProfileData?.height) > 500) newErrors.height = "Height must be in between 50-500 cm";
        else if (!userProfileData?.weight) newErrors.weight = "Please enter your weight.";
        else if (Number(userProfileData?.weight) < 5 || Number(userProfileData?.weight) > 400) newErrors.weight = "Weight must be in between 5-400 kg";
        else if (!userProfileData?.dietPreference) newErrors.dietPreference = "Please select your diet preference.";

        setLocalErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleNext = () => {
        if (validate()) {
            if (currentOnboardingStep < 5) {
                setProfileOnboardingData({ ...userProfileData });
                setOnboardingStep(currentOnboardingStep + 1);
            }
        }
    };

    const handleBack = () => {
        if (currentOnboardingStep > 1) {
            setOnboardingStep(currentOnboardingStep - 1);
        }
    };

    const dismissKeyboardAndDropdowns = () => {
        Keyboard.dismiss();
        setCurrentOpenDropdown(null);
    };

    return (
        <KeyboardAwareScrollView
            // behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={{ flex: 1 }}
            enabled
            bottomOffset={10}
            contentContainerStyle={{ flexGrow: 1 }}
            extraKeyboardSpace={10}
        // keyboardVerticalOffset={Platform.OS === "ios" ? 10 : 0}
        >
            <ScrollView
                contentContainerStyle={{ flexGrow: 1 }}
                keyboardShouldPersistTaps="handled"
                // bounces={false}
                onStartShouldSetResponder={() => true}
                showsVerticalScrollIndicator={false}
                onTouchStsart={() => Keyboard.dismiss()}
            >
                <TouchableWithoutFeedback onPress={dismissKeyboardAndDropdowns}>
                    <View style={styles.container}>
                        <CustomAlert
                            visible={showPermissionAlert}
                            title="Allow Access"
                            message="This app needs access to your media & notifications."
                            icon={Lemon}
                            buttons={[
                                { text: "Allow", onPress: () => console.log("Permission Granted"), style: "allowButton" },
                                { text: "Deny", onPress: () => console.log("Permission Denied"), style: "denyButton" },
                            ]}
                            onClose={() => setShowPermissionAlert(false)}
                        />
                        <Image source={Lemon} style={styles.backgroundImage} />
                        <View style={styles.header}>
                            <Text style={styles.headerText}>Welcome, {session?.user?.name?.trim().split(/\s+/)[0] || ''}!</Text>
                            <Text style={styles.text}>Please provide some information to get started.</Text>
                        </View>
                        <Image source={Lemon} style={styles.backgroundImage1} />
                        <View style={styles.content}>
                            <Text style={styles.title}>Profile Setup</Text>
                            <View style={styles.form}>
                                <DateOfBirthPicker
                                    value={userProfileData.dob}
                                    placeholder="Date of Birth"
                                    onSelectDate={(dateString, age) => {
                                        setUserProfileData((prev) => ({
                                            ...prev,
                                            age: age.toString(),
                                            dob: dateString,
                                        }));
                                    }}
                                    calculatedAge={userProfileData.age ? parseInt(userProfileData.age) : undefined}
                                    error={localErrors.dob}
                                    clearValidationError={() => {
                                        setLocalErrors((prevErrors) => {
                                            return { ...prevErrors, dob: "" };
                                        });
                                    }}
                                    onPress={() => setCurrentOpenDropdown(null)}
                                />

                                <View style={{ marginVertical: 5 }}>
                                    <CustomSelect
                                        options={[
                                            { label: "Male", value: "male" },
                                            { label: "Female", value: "female" },
                                            { label: "Other", value: "others" },
                                        ]}
                                        selectedValue={userProfileData.sex}
                                        onValueChange={(value) => {
                                            setUserProfileData((prev) => ({
                                                ...prev,
                                                sex: value,
                                            }));
                                        }}
                                        label="Sex"
                                        triggerZ={10}
                                        listZ={9}
                                        currentOpenDropdown={currentOpenDropdown}
                                        setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                        dropdownId={1}
                                        clearValidationError={() => {
                                            setLocalErrors((prevErrors) => {
                                                return { ...prevErrors, sex: "" };
                                            });
                                        }}
                                    />
                                </View>
                                {localErrors.sex && <Text style={styles.error}>{localErrors.sex}</Text>}

                                <CustomInput
                                    value={userProfileData.height}
                                    onChangeText={(value) => {
                                        setUserProfileData((prev) => ({
                                            ...prev,
                                            height: value,
                                        }));
                                    }}
                                    placeholder="Height (cm)"
                                    keyboardType="number-pad"
                                    editable={true}
                                    setCloseDropdowns={() => {
                                        setCurrentOpenDropdown(null);
                                    }}
                                    integer={true}
                                    isNegative={false}
                                    clearValidationError={() => {
                                        setLocalErrors((prevErrors) => {
                                            return { ...prevErrors, height: "" };
                                        });
                                    }}
                                />
                                {localErrors.height && <Text style={styles.error}>{localErrors.height}</Text>}

                                <CustomInput
                                    value={userProfileData.weight}
                                    onChangeText={(value) => {
                                        setUserProfileData((prev) => ({
                                            ...prev,
                                            weight: value,
                                        }));
                                    }}
                                    placeholder="Weight (kg)"
                                    keyboardType="number-pad"
                                    editable={true}
                                    setCloseDropdowns={() => {
                                        setCurrentOpenDropdown(null);
                                    }}
                                    precision={4}
                                    isNegative={false}
                                    clearValidationError={() => {
                                        setLocalErrors((prevErrors) => {
                                            return { ...prevErrors, weight: "" };
                                        });
                                    }}
                                />
                                {localErrors.weight && <Text style={styles.error}>{localErrors.weight}</Text>}

                                <View style={{ marginVertical: 10 }}>
                                    <CustomSelect
                                        options={dietOptions}
                                        selectedValue={userProfileData.dietPreference}
                                        onValueChange={(value) => {
                                            setUserProfileData((prev) => ({
                                                ...prev,
                                                dietPreference: value,
                                            }));
                                        }}
                                        label="Diet Preference"
                                        triggerZ={8}
                                        listZ={7}
                                        currentOpenDropdown={currentOpenDropdown}
                                        setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                        dropdownId={2}
                                        clearValidationError={() => {
                                            setLocalErrors((prevErrors) => {
                                                return { ...prevErrors, dietPreference: "" };
                                            });
                                        }}
                                    />
                                </View>
                                {localErrors.dietPreference && <Text style={styles.error}>{localErrors.dietPreference}</Text>}

                                <View style={styles.buttonContainer}>
                                    <CustomButton title="Back" onPress={handleBack} isBack />
                                    <CustomButton title="Next" onPress={handleNext} />
                                </View>
                            </View>
                            <Image source={Leaf} style={styles.leaf_background} />
                        </View>
                    </View>
                </TouchableWithoutFeedback>
            </ScrollView>
        </KeyboardAwareScrollView>
    );
};


const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        backgroundColor: Colors.primaryGreen,
    },
    header: {
        height: '30%',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
    headerText: {
        color: Colors.white,
        fontSize: 32,
        fontFamily: 'Exo_700Bold',
        textAlign: 'center',
    },
    text: {
        color: Colors.white,
        fontFamily: 'Exo_400Regular',
        marginHorizontal: 25,
        fontSize: 14,
        textAlign: 'center',
    },
    content: {
        flex: 1,
        width: '100%',
        height: '100%',
        zIndex: 9998,
        backgroundColor: Colors.white,
        padding: 20,
        borderTopLeftRadius: 50,
        borderTopRightRadius: 50,
    },
    title: {
        fontSize: 20,
        fontFamily: 'Exo_500Medium',
        color: Colors.black,
        marginBottom: 20,
        textAlign: 'center',
    },
    form: {
        flex: 5,
        zIndex: 9998,
    },
    error: {
        color: 'red',
        marginHorizontal: 7,
        fontSize: 12,
        marginBottom: 0,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20,
        fontFamily: 'Exo_400Regular',
        zIndex: -2
    },
    backgroundImage: {
        position: 'absolute',
        top: '26%',
        right: '-19%',
        width: '40%',
        height: '40%',
        resizeMode: 'contain',
        zIndex: 9999,
        transform: [{ translateY: '-50%' }, { rotate: '90deg' }],
    },
    backgroundImage1: {
        position: 'absolute',
        top: '1%',
        left: '-8%',
        width: '35%',
        height: '35%',
        resizeMode: 'contain',
        zIndex: 5,
        transform: [{ translateY: '-50%' }, { rotate: '-10deg' }],
    },
    leaf_background: {
        position: 'absolute',
        bottom: '-33%',
        right: '21%',
        width: '70%',
        height: '40%',
        resizeMode: 'contain',
        zIndex: -1,
        transform: [{ translateY: '-50%' }, { rotate: '-5deg' }],
    },
});