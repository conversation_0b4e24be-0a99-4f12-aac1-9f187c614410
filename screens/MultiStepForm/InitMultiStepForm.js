import React, { useEffect } from 'react';
import { StyleSheet, View, Button, KeyboardAvoidingView, ScrollView, Platform } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { ProfileSetupScreen } from './ProfileSetupScreen';
import { GoalsSetupScreen } from './GoalsSetupScreen';
import { SelectDeviceScreen } from './SelectDeviceScreen';
import { PairDeviceScreen } from './PairDeviceScreen';
import AppPermission from './AppPermission';
import useOnboardingFormStore from 'store/onboadingFormStore';
import { SelectDeviceScreen2 } from './SelectDeviceScreen2';
import ScanDeviceScreen from './ScanDeviceScreen';

const InitMultiStepForm = ({ navigation }) => {

    const { currentOnboardingStep } = useOnboardingFormStore(state => state)

    // Screen rendering based on current step
    const renderCurrentScreen = () => {
        switch (currentOnboardingStep) {
            case 1:
                return <ProfileSetupScreen />;
            case 2:
                return <GoalsSetupScreen />;
            case 3:
                return <AppPermission />;
            case 4:
                return <SelectDeviceScreen2 />;
            case 5:
                return <ScanDeviceScreen navigation={navigation} />;
            default:
                return null;
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.container}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 80 : -38}
        >
            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.scrollContent}
            >
                <Animated.View style={[styles.animatedContainer]}>
                    {renderCurrentScreen()}
                </Animated.View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

export default InitMultiStepForm;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignContent: 'center',
    },
    scrollContent: {
        flexGrow: 1,
    },
    animatedContainer: {
        flex: 1,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 20,
    },
});
