import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, Text, View, Dimensions, TouchableOpacity, Image } from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import { Colors } from 'constants/theme/colors'; // Adjust or replace with your color values
import { ThemeFonts } from 'constants/theme/fonts';

const { width } = Dimensions.get('window');

const data = [
    {
        tag: 'Updates',
        greeting: 'Hello <PERSON>!',
        subheading: 'How are you feeling today?',
        content: `You've been consistent with your goals! Keep up the great work with your daily activity tracking.`
    },
    {
        tag: 'Nutrition Tip',
        greeting: 'Protein Power',
        subheading: 'Add more protein to your meals for better results',
        content: `Including protein in every meal helps control hunger and supports muscle recovery after workouts.`
    },
    {
        tag: 'Activity',
        greeting: 'Move More Today',
        image: 'https://images.unsplash.com/photo-1519681393784-d120267933ba?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop',
        subheading: `You're 2,000 steps away from your daily goal`,
        content: `A quick 15-minute walk can help you reach your step goal and improve your mood and energy levels.`
    },
    {
        tag: 'Sleep Insight',
        greeting: 'Rest Well',
        subheading: 'Improve your sleep quality with these tips',
        content: `Try to maintain a consistent sleep schedule. Your recent data shows irregular sleep patterns affecting your recovery.`
    }
];

const CarousalSection = () => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const carouselRef = useRef(null);

    const handlePrev = () => {
        if (currentIndex > 0) {
            carouselRef.current?.scrollTo({ index: currentIndex - 1, animated: true });
        }
    };

    const handleNext = () => {
        if (currentIndex < data.length - 1) {
            carouselRef.current?.scrollTo({ index: currentIndex + 1, animated: true });
        }
    };

    return (
        <View style={styles.container}>
            <Carousel
                ref={carouselRef}
                width={width - 24}
                height={260}
                data={data}
                autoPlay
                autoPlayInterval={3000}
                scrollAnimationDuration={800}
                // onSnapToItem={setCurrentIndex}
                onProgressChange={(progress, absoluteProgress) => {
                    setCurrentIndex(Math.round(absoluteProgress) % 4);
                    // setCurrentIndex(Math.abs(Math.round(progress / (width - 24))) % 4);
                }}
                renderItem={({ item }) => (
                    <View style={{ flex: 1, paddingHorizontal: 4 }}>
                        <View style={styles.card}>
                            {item.image && (
                                <Image
                                    source={{ uri: item.image }}
                                    style={styles.backgroundImage}
                                    resizeMode="cover"
                                />
                            )}
                            <View style={styles.tagContainer}>
                                <Text style={styles.tagText} numberOfLines={1} allowFontScaling={false}>{item.tag}</Text>
                            </View>
                            <Text style={styles.greeting} numberOfLines={1} allowFontScaling={false}>{item.greeting}</Text>
                            <Text style={styles.subheading} numberOfLines={1} allowFontScaling={false}>{item.subheading}</Text>
                            <Text style={styles.content} textBreakStrategy='simple' numberOfLines={3} allowFontScaling={false}>{item.content}</Text>
                        </View>
                    </View>
                )}
                style={{ alignSelf: 'center' }}
            />
            <View style={styles.navContainer}>
                <TouchableOpacity onPress={handlePrev} activeOpacity={.85} disabled={currentIndex === 0} style={styles.arrowButton}>
                    <Text style={[styles.arrow, currentIndex === 0 && { opacity: 0.3 }]}>{'◀'}</Text>
                </TouchableOpacity>
                <Text style={styles.pagination}>{`${currentIndex + 1}/${data.length}`}</Text>
                <TouchableOpacity onPress={handleNext} activeOpacity={.85} disabled={currentIndex === data.length - 1} style={styles.arrowButton}>
                    <Text style={[styles.arrow, currentIndex === data.length - 1 && { opacity: 0.3 }]}>{'▶'}</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default CarousalSection;

const styles = StyleSheet.create({
    container: {
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        height: 260
    },
    card: {
        flex: 1,
        backgroundColor: Colors.lightPurple,
        borderRadius: 30,
        borderWidth: 2,
        borderColor: Colors.lightGreen,
        padding: 24,
        justifyContent: 'flex-start',
    },
    tagContainer: {
        alignSelf: 'flex-start',
        borderWidth: 2,
        borderColor: Colors.lightGreen,
        borderRadius: 16,
        paddingVertical: 4,
        paddingHorizontal: 16,
        marginBottom: 4,
        backgroundColor: 'transparent',
    },
    tagText: {
        color: Colors.white,
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        letterSpacing: 1,
    },
    greeting: {
        fontSize: 18,
        color: Colors.white,
        marginVertical: 12,
        fontFamily: ThemeFonts.Exo_700,
    },
    subheading: {
        fontSize: 16,
        color: Colors.white,
        marginBottom: 8,
        fontFamily: ThemeFonts.Exo_400,
    },
    content: {
        fontSize: 12,
        color: Colors.white,
        marginTop: 2,
        lineHeight: 20,
        fontFamily: ThemeFonts.Exo_400,
    },
    navContainer: {
        width: "100%",
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingBottom: 8
        // top: -50
    },
    arrowButton: {
        padding: 10,
    },
    arrow: {
        fontSize: 16,
        color: Colors.white,
        fontWeight: 'bold',
    },
    pagination: {
        color: Colors.white,
        fontSize: 12,
        marginHorizontal: 16,
        fontWeight: ThemeFonts.Exo_400,
    },
    backgroundImage: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        borderRadius: 30,
        opacity: 0.3,
    },
});
