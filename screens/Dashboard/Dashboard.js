import React, { useEffect, useState, useCallback, useMemo } from "react";
import {
    StyleSheet,
    ScrollView,
    Platform,
    RefreshControl,
    TouchableWithoutFeedback,
    View,
} from "react-native";
import { Colors } from "constants/theme/colors";
import * as Device from "expo-device";
import DeviceInfo from "react-native-device-info";
import * as Notifications from "expo-notifications";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import { ThemeFonts } from "constants/theme/fonts";

// Service imports
import apiClient from "services/axiosInstance";
import {
    getDeviceType,
    registerForPushNotificationsAsync,
} from "services/pushNotificationRegistrationService";
import { readSampleData } from "services/health/healthDataService";
import { highlightsService } from "services/highlightService";

// Component imports
import { CustomAlert } from "components/CustomAction";
import DashboardSection from "./components/DashboardSection";
import SimpleDashboardSection from "./components/SimpleDashboardSection";
import MoodDashboardSection from "./components/MoodDashboardSection";

// Store imports
import useMoodStore from "store/moodStore";
import useNutritionMealRecordStore from "store/nutritionMealRecordStore";
import useNotificationStore from "store/notificationStore";
import useDeviceTimerStore from "store/deviceTimerStore";

import AppLayout from "navigations/components/Layouts/AppLayout";

import CarousalSection from './components/CarousalSection';
import SummarySection from './components/SummarySection';
import useSleepStore from "store/sleepStore";
import useUserWeightStore from "store/userWeightStore";
import formatDuration from "screens/Menu Screens/Device/utils/formartDuration";
import NutritionStackedBarGraph from "components/Charts/BarGraphs/NutritionStackedBarGraph";


const Dashboard = () => {
    const navigation = useNavigation();
    const { setNotificationStatus } = useNotificationStore((state) => state);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [error, setError] = useState(null);

    // Nutrition data
    const { isLoadingMealRecords, lastLoggedMealRecord, mealRecords } = useNutritionMealRecordStore((state) => state);
    const { getMealRecords, getNutritionHighlights } = useNutritionMealRecordStore((state) => state);
    const { getMoodLastLogged, getMoodHighlights } = useMoodStore((state) => state);

    const { totalUserTime, getTimerHistory, getTimerHighlights } = useDeviceTimerStore((state) => state);
    const { getLastLoggedSleep, getSleepHighLights } = useSleepStore((state) => state);
    const { getLastLoggedWeightData, getWeightHighlights } = useUserWeightStore(state => state);
    const getMealGraphRecords = useNutritionMealRecordStore((state) => state.getMealGraphRecords);

    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

    const fetchCarouselData = useCallback(async () => {
        try {
            await Promise.all([
                highlightsService.getHighlights("mood"),
                highlightsService.getHighlights("nutrition"),
                highlightsService.getHighlights("sleep"),
            ]);
        } catch (error) {
            console.error("Error fetching carousel data:", error);
        }
    }, []);

    useEffect(() => {
        let isMounted = true;

        const setupNotifications = async () => {
            try {
                const { status: existingStatus } =
                    await Notifications.getPermissionsAsync();

                if (existingStatus === "granted") return;

                const pushTokenRes = await registerForPushNotificationsAsync();

                if (
                    pushTokenRes.success &&
                    pushTokenRes?.pushTokenString &&
                    isMounted
                ) {
                    const deviceId = await DeviceInfo.getUniqueId();

                    const deviceData = {
                        notificationToken: pushTokenRes?.pushTokenString,
                        deviceId: deviceId,
                        deviceType: getDeviceType(Device.deviceType),
                        osType: Platform.OS,
                    };

                    const res = await apiClient.post(`/notification_token`, deviceData);
                    setNotificationStatus({
                        isNotificationActive: res.data.isNotificationActive,
                    });
                }
            } catch (error) {
                console.error("Error setting up notifications:", error);
            }
        };

        setupNotifications();

        return () => {
            isMounted = false;
        };
    }, []);

    useEffect(() => {
        const initializeData = async () => {
            getMealRecords(),
                getTimerHistory(),
                getTimerHighlights(),
                getMoodLastLogged(),
                getNutritionHighlights(),
                getLastLoggedSleep(),
                getSleepHighLights(),
                getLastLoggedWeightData(),
                getWeightHighlights(),
                getMoodHighlights(),
                getMealGraphRecords();
        };

        initializeData();
        return () => {
            // Cleanup function when screen loses focus
        };
    }, []);

    const nutritionTotals = useMemo(() => {
        if (!mealRecords || mealRecords.length === 0) {
            return { calories: 0, protein: 0, fiber: 0 };
        }

        return mealRecords.reduce(
            (totals, mealRecord) => {
                if (!mealRecord.meals) return totals;

                mealRecord.meals.forEach((meal) => {
                    totals.calories += meal.calories || 0;
                    totals.protein += meal.protein || 0;
                    totals.fiber += meal.fiber || 0;
                });

                return totals;
            },
            { calories: 0, protein: 0, fiber: 0 }
        );
    }, [mealRecords]);

    // Handle refresh data with improved error handling
    const handleRefreshData = useCallback(async () => {
        if (isRefreshing) return; // Prevent multiple refreshes

        setIsRefreshing(true);
        // Refresh all data
        try {
            await Promise.all([
                // fetchCarouselData(),
                getMealRecords(),
                getMealGraphRecords(),
                getTimerHistory(),
                getTimerHighlights(),
                getMoodLastLogged(),
                getNutritionHighlights(),
                getLastLoggedSleep(),
                getSleepHighLights(),
                getLastLoggedWeightData(),
                getWeightHighlights(),
                getMoodHighlights()
            ]);

            // Handle sample data separately with better error handling
            try {
                const res = await readSampleData(1);
                if (!res.success) {
                    setError(res.error);
                }
            } catch (sampleError) {
                console.error("Error reading sample data:", sampleError);
            }
        } catch (err) {
            console.error("Error refreshing data:", err);
        } finally {
            setIsRefreshing(false);
        }
    }, [
        fetchCarouselData,
        getMealRecords,
        isRefreshing,
    ]);

    return (
        <AppLayout illustration={true}>
            <ScrollView
                showsVerticalScrollIndicator={false}
                scrollEventThrottle={16}
                refreshControl={
                    <RefreshControl
                        refreshing={isRefreshing}
                        onRefresh={handleRefreshData}
                        colors={[Colors.primaryGreen]}
                        progressViewOffset={24}
                    />
                }
            >
                <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
                    <View style={styles.contentContainer}>
                        {/* <CarousalSection /> */}
                        {/* Nutrition Section */}
                        <DashboardSection
                            title="Nutrition"
                            onNavigate={() => navigation.navigate("Nutrition")}
                            metrics={[
                                { value: nutritionTotals.calories, label: "Calories" },
                                {
                                    value: nutritionTotals.protein,
                                    label: "Protein",
                                    suffix: "g",
                                },
                                { value: nutritionTotals.fiber, label: "Fiber", suffix: "g" },
                            ]}
                            headerStyle={{ padding: 10 }}
                            currentOpenDropdown={currentOpenDropdown}
                            setCurrentOpenDropdown={setCurrentOpenDropdown}
                            timestamp={lastLoggedMealRecord?.updatedAt}
                            isLoading={isLoadingMealRecords}
                        />
                        <View style={{ marginBottom: 16, marginTop: 4 }}>
                            <NutritionStackedBarGraph
                                currentOpenDropdown={currentOpenDropdown}
                                setCurrentOpenDropdown={setCurrentOpenDropdown}
                            />
                        </View>

                        {/* Device Summary Section */}
                        <SimpleDashboardSection
                            title="Device"
                            onNavigate={() => navigation.navigate("Device")}
                            backgroundColor={Colors.primaryGreen}
                            totalUsageTime={formatDuration(totalUserTime)}
                        />

                        {/* Mood Summary Section */}
                        <MoodDashboardSection
                            title="Mood"
                            onNavigate={() => navigation.navigate("Mood")}
                            backgroundColor={Colors.primaryPurple}
                        />

                        {/* Activity Summary Section */}
                        <DashboardSection
                            title="Activity"
                            onNavigate={() => navigation.navigate("Activity")}
                            metrics={[
                                { value: "6,000", label: "Steps" },
                                { value: "200", label: "Active Energy" },
                            ]}
                            headerStyle={{ padding: 10 }}
                            isLoading={isLoadingMealRecords}
                        />
                        <SummarySection
                            title="Summary"
                            onNavigate={() => navigation.navigate("Summary")}
                            headerStyle={{ padding: 10, paddingVertical: 20 }}
                        />

                        {/* {error && (
                            <CustomAlert
                                title="Error"
                                message={error}
                                visible={!!error}
                                buttons={[
                                    {
                                        text: "OK",
                                        onPress: () => setError(null),
                                        style: "allowButton",
                                    },
                                ]}
                                onClose={() => setError(null)}
                            />
                        )} */}
                    </View>
                </TouchableWithoutFeedback>
            </ScrollView>
        </AppLayout>
    );
};

const styles = StyleSheet.create({
    contentContainer: {
        flexGrow: 1,
        paddingBottom: 150,
        padding: 0,
    },
    scrollContainer: {
        paddingBottom: 64,
        paddingHorizontal: 24,
    },
    // Carousel styles
    carouselContainer: {
        marginVertical: 20,
        alignItems: "center",
    },
    carouselItem: {
        flex: 1,
        borderRadius: 25,
        overflow: "hidden",
        justifyContent: "center",
        padding: 20,
    },
    carouselImage: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity: 0.6,
    },
    carouselContent: {
        padding: 16,
    },
    carouselCategory: {
        fontSize: 14,
        color: Colors.white,
        fontFamily: ThemeFonts.Exo_600,
        marginBottom: 8,
    },
    carouselTitle: {
        fontSize: 24,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.white,
        marginBottom: 8,
    },
    carouselDescription: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.white,
    },
    paginationContainer: {
        flexDirection: "row",
        justifyContent: "center",
        marginTop: 10,
    },
    paginationDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: Colors.primaryGreen,
        marginHorizontal: 4,
    },
    // Nutrition section styles
    sectionContainer: {
        marginVertical: 20,
    },
    sectionHeader: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 16,
    },
    sectionTitle: {
        fontSize: 28,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.black,
    },
    sectionHeaderRight: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: Colors.primaryPurple,
        // borderWidth: 1,
        // borderColor: Colors.primaryGreen,
        width: 100,
        borderRadius: 25,
        paddingVertical: 4,
        paddingHorizontal: 8,
        textAlign: "center",
    },
    lastLogTimeText: {
        fontSize: 12,
        color: Colors.black,
        textAlign: "start",
        fontFamily: ThemeFonts.Exo_500,
        marginRight: 8,
    },
    nutritionCard: {
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: Colors.primaryGreen,
        padding: 16,
        borderRadius: 25,
    },
    nutritionMetric: {
        alignItems: "center",
        flex: 1,
        justifyContent: "center",
    },
    metricValue: {
        fontSize: 22,
        fontFamily: ThemeFonts.Lexend_700,
        color: Colors.white,
        marginTop: 8,
    },
    metricLabel: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.white,
        marginTop: 4,
    },
    verticalDivider: {
        width: 1,
        height: 50,
        backgroundColor: Colors.white,
        marginHorizontal: 8,
    },
    // Cards section styles
    cardsContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "space-between",
        marginBottom: 20,
    },
    card: {
        backgroundColor: Colors.white,
        borderRadius: 15,
        padding: 16,
        width: "31%",
        alignItems: "center",
        elevation: 2,
        marginBottom: 16,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    cardTitle: {
        fontSize: 14,
        color: "#666",
        marginTop: 8,
        textAlign: "center",
    },
    cardValue: {
        fontSize: 16,
        fontWeight: "bold",
        color: Colors.primaryGreen,
        marginTop: 4,
    },
    loaderContainer: {
        height: 200,
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
    },
});

export default Dashboard;


// import { StyleSheet, Text, View } from 'react-native'
// import React from 'react'
// import AppLayout from 'navigations/components/Layouts/AppLayout'

// const Dashboard = () => {
//     return (
//         <AppLayout>
//             <Text>Dashboard</Text>
//         </AppLayout>
//     )
// }

// export default Dashboard

// const styles = StyleSheet.create({})