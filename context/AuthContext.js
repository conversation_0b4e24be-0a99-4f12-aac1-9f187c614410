import React, { createContext, useReducer, useEffect, useContext, useCallback } from 'react';
import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';
import apiClient from 'services/axiosInstance';
import userService from 'services/userService';
import { getDeviceType, registerForPushNotificationsAsync } from 'services/pushNotificationRegistrationService';
import DeviceInfo from 'react-native-device-info';
import * as Device from "expo-device";
import { Platform } from 'react-native';

const apiUrl = process.env.EXPO_PUBLIC_API_URL;

export const AuthContext = createContext();

const authReducer = (state, action) => {
    switch (action.type) {
        case 'RESTORE_TOKEN':
            return {
                ...state,
                userToken: action.token,
                user: action.user,
                isLoading: false,
            };
        case 'SIGN_IN':
            return {
                ...state,
                userToken: action.token,
                user: action.user,
                isSignout: false,
                isLoading: false,
            };
        case 'SIGN_OUT':
            return {
                ...state,
                userToken: null,
                user: null,
                isSignout: true,
                isLoading: false,
            };
        case 'SET_LOADING':
            return {
                ...state,
                isLoading: action.isLoading,
            };
        case 'SET_UPDATING':
            return {
                ...state,
                // isUpdating: action.isUpdating,
            };
        case 'UPDATE_USER':
            return {
                ...state,
                user: action.user,
                // isUpdating: false
            };
        default:
            return state;
    }
};
const AuthProvider = ({ children }) => {
    const [state, dispatch] = useReducer(authReducer, {
        isLoading: false,
        userToken: null,
        user: null,
        isSignout: false,
    });

    useEffect(() => {
        const bootstrapAsync = async () => {
            try {
                dispatch({ type: 'SET_LOADING', isLoading: true });
                const accessToken = await SecureStore.getItemAsync('accessToken');
                const userData = await SecureStore.getItemAsync('userData');
                if (accessToken && userData) {
                    const user = JSON.parse(userData);
                    // Set the token in axios headers for subsequent requests
                    apiClient.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
                    dispatch({
                        type: 'RESTORE_TOKEN',
                        token: accessToken,
                        user: user
                    });
                } else {
                    dispatch({
                        type: 'RESTORE_TOKEN',
                        token: null,
                        user: null
                    });
                }
            } catch (error) {
                console.error('Error restoring token:', error);
                dispatch({
                    type: 'RESTORE_TOKEN',
                    token: null,
                    user: null
                });
            }
        };
        bootstrapAsync();
    }, []);

    const signIn = async (email, password, pushTokenString) => {
        try {
            // dispatch({ type: 'SET_LOADING', isLoading: true });
            const response = await apiClient.post('/login', { email, password });
            const { error, msg, accessToken, user, expiry } = response;
            if (error || !accessToken || !user) {
                throw new Error(msg || 'Invalid response from server');
            }

            // Store token and user data
            await SecureStore.setItemAsync('accessToken', accessToken);
            // Set token in axios headers
            apiClient.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;

            let res = {
                data: { isNotificationActive: false }
            };

            if (pushTokenString) {
                const deviceId = await DeviceInfo.getUniqueId();
                const deviceData = {
                    notificationToken: pushTokenString,
                    deviceId: deviceId,
                    deviceType: getDeviceType(Device.deviceType),
                    osType: Platform.OS,
                };

                res = await apiClient.post(`/notification_token`, deviceData);
            }

            await SecureStore.setItemAsync('userData', JSON.stringify(user));

            dispatch({ type: 'SIGN_IN', token: accessToken, user });

            return { success: true, isNotificationActive: res?.data?.isNotificationActive };
        } catch (error) {
            console.error('Login failed:', error);
            // dispatch({ type: 'SET_LOADING', isLoading: false });
            return { success: false, error: error };
        }

    };
    const signOut = async () => {
        try {
            dispatch({ type: 'SET_LOADING', isLoading: true });
            const deviceId = await DeviceInfo.getUniqueId();

            try {
                // Call logout API - but don't let it block the logout process if it fails
                await apiClient.get(`/logout/${deviceId}`);
            } catch (apiError) {
                console.log('Error calling logout API, but continuing with local logout:', apiError);
            }

            // Clear local storage
            await SecureStore.deleteItemAsync('accessToken');
            await SecureStore.deleteItemAsync('userData');
            await SecureStore.deleteItemAsync('refreshToken');

            // Remove token from axios headers
            delete apiClient.defaults.headers.common['Authorization'];

            dispatch({ type: 'SIGN_OUT' });
            return true;
        } catch (error) {
            console.error('Error during logout:', error);

            // Still try to clear storage and sign out even if there was an error
            try {
                await SecureStore.deleteItemAsync('accessToken');
                await SecureStore.deleteItemAsync('userData');
                await SecureStore.deleteItemAsync('refreshToken');
                delete apiClient.defaults.headers.common['Authorization'];
            } catch (storageError) {
                console.error('Error clearing storage during logout:', storageError);
            }

            dispatch({ type: 'SIGN_OUT' });
            return false;
        } finally {
            dispatch({ type: 'SET_LOADING', isLoading: false });
        }
    };

    const updateUserProfile = async (userData) => {
        try {
            // dispatch({ type: 'SET_UPDATING', isUpdating: true });
            const response = await userService.updateUser({
                ...userData,
                weight: Number(userData.weight),
            });

            if (!response.success) {
                throw new Error(response.error);
            }

            // Update user data in secure storage
            await SecureStore.setItemAsync('userData', JSON.stringify(response.data));

            // Update state
            dispatch({ type: 'UPDATE_USER', user: response.data });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            // console.error('Error updating user profile:', error.message);
            // dispatch({ type: 'SET_UPDATING', isUpdating: false });
            return {
                success: false,
                error: error?.message || "Error updating user profile"
            };
        }
    };

    const updateUserProfileLocal = async (userData) => {
        try {
            // Update user data in secure storage
            await SecureStore.setItemAsync('userData', JSON.stringify(userData));

            // Update state
            dispatch({ type: 'UPDATE_USER', user: userData });

            return true;
        } catch (error) {
            console.error('Error updating user profile:', error);
            return false;
        }
    };

    return (
        <AuthContext.Provider value={{ state, signIn, signOut, updateUserProfile, updateUserProfileLocal }}>
            {children}
        </AuthContext.Provider>
    );
};
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
export { AuthProvider };